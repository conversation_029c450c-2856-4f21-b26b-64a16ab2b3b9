#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Fix string concatenation issues in converted Python files
"""

import re
import sys

def fix_string_concatenation(content):
    """Fix broken string concatenation from f-string conversion"""
    
    # Pattern to match broken string concatenation in Exception calls
    # This matches cases like:
    # raise Exception("text".format(...) 
    #     "more text")
    
    lines = content.split('\n')
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this line has a broken string concatenation
        if ('raise Exception(' in line and 
            line.strip().endswith('".format(') and 
            i + 1 < len(lines) and 
            lines[i + 1].strip().startswith('"') and 
            lines[i + 1].strip().endswith('")')):
            
            # This is a broken string concatenation
            current_line = line.strip()
            next_line = lines[i + 1].strip()
            
            # Extract the format call and the continuation string
            format_part = current_line
            continuation_part = next_line[1:-2]  # Remove quotes and closing paren
            
            # Find the format arguments
            format_start = format_part.find('.format(')
            if format_start != -1:
                before_format = format_part[:format_start]
                format_args = format_part[format_start + 8:]  # Skip '.format('
                
                # Remove the trailing quote and add the continuation
                if before_format.endswith('"'):
                    combined_string = before_format[:-1] + continuation_part + '"'
                    fixed_line = combined_string + '.format(' + format_args
                    fixed_lines.append(fixed_line)
                    i += 2  # Skip the next line
                    continue
        
        fixed_lines.append(line)
        i += 1
    
    return '\n'.join(fixed_lines)

def main():
    if len(sys.argv) != 2:
        print("Usage: python fix_string_concat.py file_to_fix.py")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        with open(filename, 'r') as f:
            content = f.read()
        
        # Apply fixes
        content = fix_string_concatenation(content)
        
        with open(filename, 'w') as f:
            f.write(content)
        
        print("String concatenation issues fixed in: {0}".format(filename))
        
    except Exception as e:
        print("Error: {0}".format(e))
        sys.exit(1)

if __name__ == "__main__":
    main()
