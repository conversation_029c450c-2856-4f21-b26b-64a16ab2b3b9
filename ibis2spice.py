#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IBIS to SPICE Converter
将IBIS模型转换为SPICE子电路

使用pybis.py解析.ibs文件，提取模型数据，
并将其转换为SPICE子电路格式，输出到.sp文件中。
"""

# from __future__ import print_function
import sys
import os
import math
import json
from pybis import IBSParser

def format_spice_value(value, unit=''):
    """
    将数值格式化为SPICE格式
    """
    if value is None or value == 'None':
        return '0'
    
    try:
        val = float(value)
        if val == 0:
            return '0'
        
        # 使用科学记数法或工程记数法
        if abs(val) >= 1e-3 and abs(val) < 1e3:
            return "{:.6g}{}".format(val, unit)
        else:
            return "{:.6e}{}".format(val, unit)
    except:
        return str(value)

def extract_iv_data(iv_range, type_num):
    """
    从IBIS Range对象中提取I-V数据
    返回(voltage_list, current_list)
    """
    if iv_range is None or iv_range[type_num] is None or len(iv_range) == 0:
        return [], []

    # 获取典型值数据 (index 0)
    typ_data = iv_range[type_num]
    if typ_data is None:
        return [], []

    # 解析字符串格式的数据
    data_str = str(typ_data)

    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            voltage_str = data_str[start1:end1]

            # 找到第二个列表的开始和结束
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            current_str = data_str[start2:end2]

            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass

            # 解析电流列表
            currents = []
            for i in current_str.split(', '):
                try:
                    currents.append(float(i.strip()))
                except:
                    pass

            return voltages, currents
        except Exception as e:
            # print("Error parsing I-V data: {}".format(e))
            # print("Data string: {}".format(data_str))
            pass

    return [], []

def extract_waveform_data(waveform_list, type_num):
    """
    从IBIS波形数据中提取时间-电压数据
    返回(time_list, voltage_list)
    """
    if not waveform_list or len(waveform_list) == 0:
        return [], []

    # 获取第一个波形数据（典型值）
    waveform = waveform_list[type_num]
    if 'waveform' not in waveform:
        return [], []

    waveform_data = waveform['waveform'][type_num]  # 获取典型值
    if waveform_data is None:
        return [], []

    # 解析字符串格式的数据
    data_str = str(waveform_data)

    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束（时间）
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            time_str = data_str[start1:end1]

            # 找到第二个列表的开始和结束（电压）
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            voltage_str = data_str[start2:end2]

            # 解析时间列表
            times = []
            for t in time_str.split(', '):
                try:
                    times.append(float(t.strip()))
                except:
                    pass

            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass

            return times, voltages
        except Exception as e:
            # print("Error parsing waveform data: {}".format(e))
            # print("Data string: {}".format(data_str))
            pass

    return [], []

def create_pwl_source(name, voltages, currents, node1, node2):
    """
    创建分段线性电流源
    """
    if not voltages or not currents or len(voltages) != len(currents):
        return "* Warning: Invalid I-V data for {}\n".format(name)
    
    spice_lines = []
    spice_lines.append("* {} I-V characteristic".format(name))
    
    # 创建电压控制电流源
    spice_lines.append("G{} {} {} VCCS PWL(".format(name, node1, node2))
    
    # 添加PWL数据点
    for i, (v, i_val) in enumerate(zip(voltages, currents)):
        if i % 4 == 0 and i > 0:  # 每行4个数据点
            spice_lines.append("")
            spice_lines.append("+")
        spice_lines.append(" {}V {}A".format(format_spice_value(v), format_spice_value(i_val)))
    
    spice_lines.append(" )")
    spice_lines.append("")
    
    return "\n".join(spice_lines)

def determine_model_features(model_type):
    """
    根据IBIS模型类型确定需要的功能特性
    返回: (needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential)
    """
    # 标准化模型类型名称（去除大小写差异和特殊字符）
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')

    # 需要ENABLE信号的模型类型
    enable_types = {
        '3state', 'io', 'ioopendrain', 'ioopensink', 'ioopensource',
        # '3stateecl', 'ioecl', 'iodiff', '3statediff', 'seriesswitch'
    }

    # 需要波形数据的模型类型（有输出驱动能力的）
    waveform_types = {
        'output', '3state', 'io', 'opendrain', 'ioopendrain',
        'opensink', 'ioopensink', 'opensource', 'ioopensource',
        # 'outputecl', '3stateecl', 'ioecl', 'outputdiff',
        # '3statediff', 'iodiff', 'series', 'seriesswitch'
    }

    # 需要Pullup/Pulldown的模型类型（推挽输出）
    pullup_pulldown_types = {
        'output', '3state', 'io', 
        # 'series', 
        # 'outputecl', '3stateecl', 'ioecl', 
        # 'outputdiff', '3statediff', 'iodiff'
    }

    # 差分信号类型
    differential_types = {
        'inputdiff', 'outputdiff', 'iodiff', '3statediff'
    }

    # 开漏/开源类型（只有单向驱动）
    open_drain_types = {
        'opendrain', 'ioopendrain', 'opensource', 'ioopensource', 'opensink', 'ioopensink'
    }

    needs_enable = model_type_norm in enable_types
    needs_waveforms = model_type_norm in waveform_types
    needs_pullup_pulldown = model_type_norm in pullup_pulldown_types
    needs_differential = model_type_norm in differential_types

    return needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential

def get_model_type_description(model_type):
    """
    获取IBIS模型类型的详细描述
    """
    descriptions = {
        'Input': 'Input buffer - receives signals only',
        'Output': 'Output buffer - drives signals with push-pull output',
        'I/O': 'Bidirectional I/O buffer - can both receive and drive',
        '3-state': 'Three-state buffer - output can be disabled (Hi-Z)',
        'Open_drain': 'Open drain output - can only pull low',
        'I/O_open_drain': 'Bidirectional open drain I/O',
        'Open_sink': 'Open sink output - current sink only',
        'I/O_open_sink': 'Bidirectional open sink I/O',
        'Open_source': 'Open source output - current source only',
        'I/O_open_source': 'Bidirectional open source I/O',
        # 'Input_ECL': 'ECL input buffer - differential ECL logic',
        # 'Output_ECL': 'ECL output buffer - differential ECL driver',
        # 'I/O_ECL': 'Bidirectional ECL I/O buffer',
        # '3-state_ECL': 'Three-state ECL buffer with enable control',
        # 'Input_diff': 'Differential input buffer - LVDS/CML receiver',
        # 'Output_diff': 'Differential output buffer - LVDS/CML driver',
        # 'I/O_diff': 'Bidirectional differential I/O buffer',
        # '3-state_diff': 'Three-state differential buffer',
        # 'Series': 'Series termination buffer - source termination',
        # 'Series_switch': 'Switchable series termination buffer',
        # 'Terminator': 'Passive termination element'
    }

    return descriptions.get(model_type, 'Unknown model type: {}'.format(model_type))

def add_wave_test_information(waveform_info, spice_content):
        # 添加波形测试条件信息
    if 'R_fixture' in waveform_info:
        spice_content.append("* R_fixture = {}".format(format_spice_value(waveform_info['R_fixture'], 'Ohm')))
    if 'V_fixture' in waveform_info:
        v_fix = waveform_info['V_fixture'][0] if isinstance(waveform_info['V_fixture'], list) else waveform_info['V_fixture']
        spice_content.append("* V_fixture = {}".format(format_spice_value(v_fix, 'V')))
    if 'C_fixture' in waveform_info:
        spice_content.append("* C_fixture = {}".format(format_spice_value(waveform_info['C_fixture'], 'F')))
    return spice_content

def build_table_pairs(table_pairs, table_line, spice_content):
    # 每行最多5个数据对，避免行过长
    pairs_per_line = 5
    for i in range(0, len(table_pairs), pairs_per_line):
        line_pairs = table_pairs[i:i+pairs_per_line]
        if i == 0:
            spice_content.append(table_line + " ".join(line_pairs))
        else:
            spice_content.append("+ " + " ".join(line_pairs))
    spice_content.append("")
    return spice_content

def build_pwl_pairs(times, voltages, pwl_line, spice_content):
    # 构建PWL数据点
    pwl_pairs = []
    for t, v in zip(times, voltages):
        pwl_pairs.append("{} {}".format(format_spice_value(t, 's'), format_spice_value(v, 'V')))

    # 每行最多4个时间-电压对
    pairs_per_line = 4
    for i in range(0, len(pwl_pairs), pairs_per_line):
        line_pairs = pwl_pairs[i:i+pairs_per_line]
        if i == 0:
            spice_content.append(pwl_line + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
        else:
            spice_content.append("+ " + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
    spice_content.append("")
    return spice_content

def convert_ibis_to_spice(root, model_name, component_name, output_file, set_all_data):
    """
    将IBIS模型转换为SPICE子电路
    """
    type = {"typ":0, "min":1, "max":2}

    if not set_all_data["status"]:
        package_type_num = type[set_all_data["package_type"]]
        pin_type_num = type[set_all_data["pin_type"]]
        model_type_num = type[set_all_data["model_type"]]
        waveform_type_num = type[set_all_data["waveform_type"]]
        pullup_type_num = type[set_all_data["pullup_type"]]
        pulldown_type_num = type[set_all_data["pulldown_type"]]
        powerclamp_type_num = type[set_all_data["powerclamp_type"]]
        gndclamp_type_num = type[set_all_data["gndclamp_type"]]

    else:
        package_type_num = type[set_all_data["type"]]
        pin_type_num = type[set_all_data["type"]]
        model_type_num = type[set_all_data["type"]]
        waveform_type_num = type[set_all_data["type"]]
        pullup_type_num = type[set_all_data["type"]]
        pulldown_type_num = type[set_all_data["type"]]
        powerclamp_type_num = type[set_all_data["type"]]
        gndclamp_type_num = type[set_all_data["type"]]

    if 'Model' not in root or model_name not in root['Model']:
        # print("Error: Model '{}' not found in IBIS file".format(model_name))
        return False

    model = root['Model'][model_name]

    # 检查模型类型
    model_type = model.get('Model_type', '3-state')  # 默认为3-state
    # print("Model type: {}".format(model_type))

    # 根据模型类型确定端口和功能
    needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential = determine_model_features(model_type)

    # print("Features: Enable={}, Waveforms={}, Pullup/Pulldown={}, Differential={}".format(needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential))

    # 开始生成SPICE文件
    spice_content = []
    spice_content.append("* SPICE Subcircuit generated from IBIS model")
    spice_content.append("* Model: {}".format(model_name))
    spice_content.append("* Model Type: {}".format(model_type))
    spice_content.append("* Generated by ibis2spice.py")
    spice_content.append("")

    # 添加模型类型说明
    model_description = get_model_type_description(model_type)
    if model_description:
        spice_content.append("* {}".format(model_description))
        spice_content.append("")

    # 根据模型类型生成子电路定义
    if needs_differential :
        if model_type.lower() == 'inputdiff':
        # 差分信号需要正负两个端口
            ports = ["PAD_P", "PAD_N", "PC", "GC"]
        else:
            ports = ["T", "PU", "PD", "OUT_P", "OUT_N", "PC", "GC"]
    else:
        if model_type.lower() == 'input':
            ports = ["IO", "PC", "GC"]
        else:
        # 单端信号
            ports = ["T", "PU", "PD", "IO", "PC", "GC"]

    if needs_enable:
        ports.append("E")

    if needs_waveforms:
        if needs_differential:
            ports.extend(["PAD_P_RISE", "PAD_P_FALL", "PAD_N_RISE", "PAD_N_FALL"])
        else:
            ports.extend(["PAD_RISE", "PAD_FALL"])

    # 子电路定义
    # 获取第一个组件名称
    spice_content.append(".SUBCKT {} {}".format(model_name, ' '.join(ports)))
    # spice_content.extend(port_comments)
    spice_content.append("")

    # 添加阈值参数定义
    # spice_content.append("* Threshold voltage parameters for conditional logic")
    # spice_content.append(".PARAM VTH_HIGH={{VCC*0.7}}")
    # spice_content.append(".PARAM VTH_LOW={{VCC*0.3}}")
    # spice_content.append(".PARAM VTH_EN={{VCC*0.5}}")
    # spice_content.append(".PARAM VCLAMP_POS=0.3")
    # spice_content.append(".PARAM VCLAMP_NEG=-0.3")
    # spice_content.append("")

    # 添加封装参数（如果存在组件信息）
    if component_name and 'Package' in root['Component'][component_name]:
        pkg = root['Component'][component_name]['Package']
        if 'R_pkg' in pkg:
            spice_content.append("R_pkg DIE TEMP {}".format(format_spice_value(pkg['R_pkg'][package_type_num], 'Ohm')))
        if 'L_pkg' in pkg:
            spice_content.append("L_pkg TEMP IO {}".format(format_spice_value(pkg['L_pkg'][package_type_num], 'H')))
        if 'C_pkg' in pkg:
            spice_content.append("C_pkg IO 0 {}".format(format_spice_value(pkg['C_pkg'][package_type_num], 'F')))
    else:
        # 默认封装参数
        spice_content.append("R_pkg DIE TEMP 0.1Ohm")
        spice_content.append("L_pkg TEMP IO 1nH")
        spice_content.append("C_pkg IO 0 1pF")

    if model_type.lower() == 'input':
        VHI = model.get('Vinh', 3.3)
        VLO = model.get('Vinl', 0)
        spice_content.append(".PARAM VHI={}".format(VHI))
        spice_content.append(".PARAM VLO={}".format(VLO))
        print(spice_content)
    else:
        VTH = model.get('Vref', 0)
        spice_content.append(".PARAM VTH={}".format(VTH))

    # 添加模型参数
    spice_content.append("* Input/Output Capacitance:")
    for _ in ['C_comp', 'C_comp_pullup', 'C_comp_pulldown', 'C_comp_power_clamp', 'C_comp_gnd_clamp']:
        if _ in model:
            c_comp = model[_][model_type_num] if isinstance(model[_], list) else model[_]
            spice_content.append("{} DIE 0 {}".format(_, format_spice_value(c_comp, 'F')))
    spice_content.append("")

    # 电压范围信息
    if 'Voltage Range' in model:
        vrange = model['Voltage Range']
        spice_content.append("* Voltage Range: {}".format(vrange))
        spice_content.append("")

    # 温度范围信息
    if 'Temperature Range' in model:
        trange = model['Temperature Range']
        spice_content.append("* Temperature Range: {}".format(trange))
        spice_content.append("")

    # 检查是否为开漏/开源类型
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')
    is_open_drain = 'opendrain' in model_type_norm or 'opensink' in model_type_norm
    is_open_source = 'opensource' in model_type_norm

    # 添加条件控制逻辑
    VE = 1
    VT = 0.675
    if needs_pullup_pulldown:
        TE_line = "B_logicpu en_pu 0 V = (V(E) >= {}) && (V(T)  > {}) ? 1 : 0\n".format(VE, VT)
        TE_line += "B_logicpd en_pd 0 V = (V(E) >= {}) && (V(T)  < {}) ? 1 : 0\n".format(VE, VT)
        spice_content.append(TE_line)
        SW_line = ".model SW_ctrl SW VT=1 VH=0 RON=1 ROFF=1G\n"
        spice_content.append(SW_line)

    # 处理Pulldown特性 - 添加条件控制逻辑
    if needs_pullup_pulldown and 'Pulldown' in model and not is_open_source:
        voltages, currents = extract_iv_data(model['Pulldown'], pulldown_type_num)
        if voltages and currents:
            spice_content.append("* Pulldown I-V Characteristic")
            spice_content.append("* Active when output is driving LOW")

            # 根据模型类型确定条件控制逻辑
            table_line = "S_pd_drive pd_int DIE en_pd 0 SW_ctrl\n"
            table_line += "Gpd pd_int PD TABLE {V(pd_int,PD)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                table_pairs.append("({},{})".format(format_spice_value(v), format_spice_value(i_val)))

            spice_content = build_table_pairs(table_pairs, table_line, spice_content)

    # 处理Pullup特性（排除开漏类型）- 添加条件控制逻辑
    if needs_pullup_pulldown and 'Pullup' in model and not is_open_drain:
        voltages, currents = extract_iv_data(model['Pullup'], pullup_type_num)
        if voltages and currents:
            spice_content.append("* Pullup I-V Characteristic")
            spice_content.append("* Active when output is driving HIGH")

            table_line = "S_pu_drive pu_int DIE en_pu 0 SW_ctrl\n"
            table_line += "Gpu pu_int PU TABLE {V(PU,pu_int)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i in zip(voltages, currents):
                # 对于pullup，电压相对于VCC，电流取绝对值
                table_pairs.append("({},{})".format(format_spice_value(v), format_spice_value(i)))

            spice_content = build_table_pairs(table_pairs, table_line, spice_content)

    # 处理GND Clamp - 添加条件控制逻辑
    if 'GND Clamp' in model:
        voltages, currents = extract_iv_data(model['GND Clamp'], gndclamp_type_num)
        if voltages and currents:
            spice_content.append("* GND Clamp I-V Characteristic")
            spice_content.append("* Active when PAD voltage is below VSS (ESD protection)")

            # GND Clamp只在PAD电压低于VSS时生效
            table_line = "Gclamp_gnd DIE GC TABLE {V(DIE, GC)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                # 对于GND clamp，使用负电压的绝对值
                table_pairs.append("({},{})".format(format_spice_value(v), format_spice_value(i_val)))

            spice_content = build_table_pairs(table_pairs, table_line, spice_content)

    # 处理POWER Clamp - 添加条件控制逻辑
    if 'POWER Clamp' in model:
        voltages, currents = extract_iv_data(model['POWER Clamp'], powerclamp_type_num)
        if voltages and currents:
            spice_content.append("* POWER Clamp I-V Characteristic")
            spice_content.append("* Active when PAD voltage is above VCC (ESD protection)")

            # POWER Clamp只在PAD电压高于VCC时生效
            table_line = "Gclamp_pwr DIE PC TABLE {V(PC,DIE)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i in zip(voltages, currents):
                # 对于POWER clamp，电压相对于VCC
                table_pairs.append("({},{})".format(format_spice_value(v), format_spice_value(i)))

            spice_content = build_table_pairs(table_pairs, table_line, spice_content)

    # 处理Ramp信息
    if 'Ramp' in model:
        ramp = model['Ramp']
        spice_content.append("* Ramp Information")
        if 'dV/dt_r' in ramp:
            dvdt_r = ramp['dV/dt_r']
            spice_content.append("* Rising edge dV/dt: {}".format(dvdt_r))
        if 'dV/dt_f' in ramp:
            dvdt_f = ramp['dV/dt_f']
            spice_content.append("* Falling edge dV/dt: {}".format(dvdt_f))
        if 'R_load' in ramp:
            r_load = ramp['R_load']
            spice_content.append("* Load resistance: {}".format(format_spice_value(r_load, 'Ohm')))
        spice_content.append("")

    # 处理Rising Waveform波形数据（仅对有波形输出的模型）
    if needs_waveforms and 'Rising Waveform' in model:
        times, voltages = extract_waveform_data(model['Rising Waveform'], waveform_type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Rising Waveform V-T Characteristic")
            waveform_info = model['Rising Waveform'][waveform_type_num]

            spice_content = add_wave_test_information(waveform_info, spice_content)
            # 创建分段线性电压源用于上升沿
            spice_content.append("* Rising edge voltage source (for timing analysis)")
            pwl_line = "Vrise_ref PAD_RISE 0 PWL("

            # 构建PWL数据点
            spice_content = build_pwl_pairs(times, voltages, pwl_line, spice_content)
        else:
            spice_content.append("* Rising Waveform data available but could not be parsed")
            spice_content.append("")

    # 处理Falling Waveform波形数据（仅对有波形输出的模型）
    if needs_waveforms and 'Falling Waveform' in model:
        times, voltages = extract_waveform_data(model['Falling Waveform'], waveform_type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Falling Waveform V-T Characteristic")
            waveform_info = model['Falling Waveform'][waveform_type_num]

            spice_content = add_wave_test_information(waveform_info, spice_content)
            # 创建分段线性电压源用于下降沿
            spice_content.append("* Falling edge voltage source (for timing analysis)")
            pwl_line = "Vfall_ref PAD_FALL 0 PWL("

            # 构建PWL数据点
            spice_content = build_pwl_pairs(times, voltages, pwl_line, spice_content)
        else:
            spice_content.append("* Falling Waveform data available but could not be parsed")
            spice_content.append("")

    # 结束子电路
    spice_content.append(".ENDS")
    spice_content.append("")

    # 写入文件
    try:
        with open(output_file, 'w') as f:
            f.write('\n'.join(spice_content))
        print("SPICE subcircuit successfully written to {}".format(output_file))
        return True
    except Exception as e:
        print("Error writing to file {}: {}".format(output_file, e))
        return False

def main():
    """
    主函数
    """
    ibis_file = sys.argv[1]
    model_name = sys.argv[2]
    component_name = sys.argv[3]
    output_file = sys.argv[4]
    # set_all_data = sys.argv[4]
    set_all_data = {"status":True, "type":"typ", "package_type":"typ", "pin_type":"typ", "model_type":"typ", "pullup_type":"typ", "pulldown_type":"max","gndclamp_type":"min","powerclamp_type":"max","waveform_type":"typ"}
    print("Parsing IBIS file: {}".format(ibis_file))
    try:
        parser = IBSParser()
        with open(ibis_file, 'r') as f:
            root = parser.parse(f)
            print(root)

        print("Successfully parsed IBIS file")
        print("Converting model '{}' to SPICE subcircuit...".format(model_name))

        # 转换为SPICE
        success = convert_ibis_to_spice(root, model_name, component_name, output_file, set_all_data)

        if success:
            print("Conversion completed successfully!")
            print("Output file: {}".format(output_file))
        else:
            print("Conversion failed!")

    except Exception as e:
        print("Error: {}".format(e))
        return 1

    return 0

if __name__ == "__main__":
    # ibis_file = "ibis/at16245.ibs"
    # model_name = "AT16245_OUT"
    # output_file = "AT16245_OUT.sp"
    # type_num = 0
    # main_process(ibis_file, model_name, output_file, type_num)
    main()
