* Simple IBIS Model Test
.title Simple IBIS Test

* Include IBIS subcircuits
.include at16245_in.sp
.include at16245_out.sp

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals
VIN IN_SIG 0 DC 2.5
VEN ENABLE 0 DC 5.0

* Load resistors
RLOAD PAD_OUT 0 50

* Reference nodes
RRISE PAD_RISE 0 1MEG
RFALL PAD_FALL 0 1MEG

* Instantiate models
XIN PAD_IN VCC VSS AT16245_IN
XOUT IN_SIG VCC VSS PAD_OUT ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Connect input to output for testing
VIN_TEST PAD_IN 0 DC 2.5

* Analysis
.op
.print dc v(PAD_IN) v(PAD_OUT) v(IN_SIG) v(ENABLE)

.end
