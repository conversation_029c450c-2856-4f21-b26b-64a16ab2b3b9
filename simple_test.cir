* Simple IBIS Model Test - Transient Analysis
.title Simple IBIS Transient Test

* Include IBIS subcircuits

.include at16245_out.sp

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals - Digital pulse train
VIN IN_SIG 0 PULSE(0 5 1n 3n 1n 10n)
VEN ENABLE 0 DC 5.0

* Load resistors and capacitors
RLOAD PAD_OUT 0 50

* Instantiate models
XOUT IN_SIG VCC VSS PAD_OUT ENABLE VCC VSS AT16245_OUT

* Analysis
.control
* Transient analysis
tran 1n 100n

* Plot waveforms
plot v(PAD_OUT) title 'Output Signal'

.endc

.end
