* Title : snps_d3m_pddrio_ns typical corner IBIS validation deck for IBIS model

* Set Temperature
.TEMP 25

.OPTION INGOLD=1 DCON=1 BRIEF=1 POST PROBE NOTOP NOELCK


Voe OE 0 1
Vdout DOUT 0 PULSE (0 1 0 1e-12 1e-12 2e-9 4e-9)


B_Drive_pad_1 vdd_PAD gnd_PAD PAD_1 DOUT OE coreOutPAD_1 vdd_PAD gnd_PAD
+ file = 'test5p0_snps.ibs'
+ model = 'SNPS_D3F_PDDRIO3440'
+ typ = typ
+ power=off

V_pad_mod_sup vdd_PAD 0 1.5
V_pad_mod_gnd gnd_PAD 0 0

.subckt SingleEndedTlineLoad_TX in refi out refo sup
C_near in refi 1e-13
T_TestModel in refi out refo Z0=5000 TD=600p
C_far out refo 4e-12
R_term_sup out sup 100
R_term_gnd out refo 100
.ends

X_TheLoad_pad_1 PAD_1 refinPAD_1 PAD_FarEnd_1 refoutPAD_1 sup_PAD SingleEndedTlineLoad_TX
Rshort_in_pad_1 refinPAD_1 0 0
Rshort_out_pad_1 refoutPAD_1 0 0
VsupTerm_pad_1 sup_PAD 0 1.5



.tran 1e-12 1e-08
.probe v(DOUT) v(PAD_1)
.probe i(V_pad_mod_sup)
.end
