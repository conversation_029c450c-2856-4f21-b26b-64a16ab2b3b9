#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
【最终修复版】Python2项目专用的Pybis桥接器

此版本解决了UnicodeEncodeError问题。
"""

import os
import sys
import subprocess


def run_pybis_converter_safe(ibis_file, model_name, output_file, 
                            python3_path="python3", 
                            pybis_script_dir="C:\\Users\\<USER>\\Desktop\\qiaojie\\pybis-master"):
    """
    通过调用Python3的ibis2spice_simple.py脚本来转换IBIS模型。
    此函数不使用任何print语句，所有信息通过返回值传递。
    """
    log = [] # 用于收集日志

    # 1. 检查输入文件
    if not os.path.exists(ibis_file):
        log.append(u"错误: IBIS文件不存在 - {}".format(ibis_file))
        return {
            'success': False,
            'message': u"IBIS文件不存在",
            'log': log
        }

    # 2. 检查转换脚本
    converter_script = os.path.join(pybis_script_dir, "ibis2spice_simple.py")
    if not os.path.exists(converter_script):
        log.append(u"错误: 转换脚本不存在 - {}".format(converter_script))
        return {
            'success': False,
            'message': u"转换脚本不存在",
            'log': log
        }

    log.append(u"输入文件和转换脚本检查通过。")
    log.append(u"准备执行转换...")
    log.append(u"Python3路径: {}".format(python3_path))
    log.append(u"转换脚本: {}".format(converter_script))

    # 3. 构建要执行的命令
    cmd = [
        python3_path,
        converter_script,
        ibis_file,
        model_name,
        output_file
    ]
    log.append(u"命令: {}".format(" ".join(cmd)))

    # 4. 执行子进程
    try:
        result_bytes = subprocess.check_output(
            cmd,
            stderr=subprocess.STDOUT,
            cwd=pybis_script_dir
        )
        result_str = _safe_decode(result_bytes)
        # log.append(u"转换成功！")
        log.append(result_str)
        return {
            'success': True,
            'message': u"转换成功",
            'output_file': output_file,
            'log': log
        }
    except subprocess.CalledProcessError as e:
        error_output = _safe_decode(e.output)
        # log.append(u"转换失败！Python3脚本返回错误码: {}".format(e.returncode))
        log.append(u"输出:\n{}".format(error_output))
        return {
            'success': False,
            'message': u"转换失败",
            'log': log
        }
    except Exception as e:
        log.append(u"执行转换时发生异常: {}".format(str(e)))
        return {
            'success': False,
            'message': u"执行异常",
            'log': log
        }


def _safe_decode(data):
    """安全地将字节数据解码为Unicode字符串。"""
    if data is None or data == b'':
        return ""
    if isinstance(data, str):
        return data
    if isinstance(data, bytes):
        for encoding in ['utf-8', 'gbk', 'latin-1']:
            try:
                return data.decode(encoding)
            except UnicodeDecodeError:
                continue
    return str(data)


# ------------------- 在Python2项目中使用 -------------------

def _safe_print(unicode_string):
    """
    一个安全的print函数，用于Python2。
    它会将Unicode字符串编码为stdout的编码，如果失败则用替代字符。
    """
    try:
        # 获取stdout的编码，如果为None则使用默认编码
        encoding = getattr(sys.stdout, 'encoding', None) or 'utf-8'
        # 将Unicode字符串编码为字节串
        byte_string = unicode_string.encode(encoding, errors='replace')
        # 写入字节串
        sys.stdout.write(byte_string + b'\n')
    except Exception as e:
        # 如果所有方法都失败，回退到写入文件
        try:
            with open('bridge_error_log.txt', 'a') as f:
                f.write("SafePrint Error: {}\n".format(str(e)))
                f.write(unicode_string.encode('utf-8', errors='replace') + '\n')
        except:
            pass  # 最后防线，什么都不做


def main():
    """
    这是一个演示函数。
    """
    # --- 🚨 关键配置：请务必修改以下变量 🚨 ---
    # --- 重要配置 ---
    # 1. 必须指定Python3的可执行文件路径
    PYTHON3_PATH = r"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"  # 请修改为你的Python3实际路径
    # 2. 指定pybis.py和ibis2spice_simple.py所在的目录
    PYBIS_SCRIPT_DIR = r"C:\Users\<USER>\Desktop\qiaojie\pybis-master"  # 请修改为你的实际路径
    # 3. 指定一个存在的IBIS文件
    IBIS_FILE = r"C:\Users\<USER>\Desktop\qiaojie\pybis-master\ibis_demo\input_demo.ibs"  # 请修改为你的实际路径

    MODEL_NAME = "AT16245_OUT"
    OUTPUT_FILE = "AT16245_OUT.sp"
    # --- 配置结束 ---

    # 检查配置
    if not os.path.exists(PYBIS_SCRIPT_DIR):
        _safe_print(u"错误: 指定的pybis目录不存在: {}".format(PYBIS_SCRIPT_DIR))
        return
    if not os.path.exists(IBIS_FILE):
        _safe_print(u"错误: 测试IBIS文件不存在: {}".format(IBIS_FILE))
        return

    # 调用转换函数
    result = run_pybis_converter_safe(
        ibis_file=IBIS_FILE,
        model_name=MODEL_NAME,
        output_file=OUTPUT_FILE,
        python3_path=PYTHON3_PATH,
        pybis_script_dir=PYBIS_SCRIPT_DIR
    )

    # === 使用 _safe_print 函数安全地输出所有日志 ===
    _safe_print(u"=== 在Python2项目中调用Python3的Pybis ===\n")
    
    for line in result['log']:
        _safe_print(line)

    # 根据结果执行后续操作
    if result['success']:
        _safe_print(u"转换成功！")
        _safe_print(u"输出文件: {}".format(result['output_file']))
    else:
        _safe_print(u"转换失败！")


if __name__ == "__main__":
    main()