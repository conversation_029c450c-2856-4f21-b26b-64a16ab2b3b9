* HSPICE Test Circuit using Native IBIS Models
* Tests AT16245 IBIS models directly in HSPICE
* Author: IBIS2SPICE Converter Test
* Date: 2025-01-01

.title IBIS Model Test - AT16245 Input/Output Buffers (HSPICE Native)

* HSPICE IBIS model inclusion
* Note: HSPICE syntax for IBIS models
.include 'ibis/at16245.ibs'

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals
* Input test signal - step from 0V to 5V
VIN_TEST IN_SIG 0 PULSE(0 5 1n 1n 1n 10n 20n)

* Enable signal for 3-state buffer
VEN ENABLE 0 PULSE(0 5 0.5n 0.1n 0.1n 15n 30n)

* Load resistors and capacitors
RLOAD_IN PAD_IN 0 1MEG
CLOAD_IN PAD_IN 0 10pF

RLOAD_OUT PAD_OUT 0 50
CLOAD_OUT PAD_OUT 0 5pF

* Transmission line (optional)
TLINE PAD_OUT PAD_RCV 0 0 Z0=50 TD=1n

* Receiver load
RRCV PAD_RCV 0 50
CRCV PAD_RCV 0 2pF

* Instantiate IBIS models using HSPICE native syntax
* Input buffer test - HSPICE IBIS syntax
XIN PAD_IN VCC VSS IBIS_MODEL=AT16245_IN IBIS_FILE='ibis/at16245.ibs'

* Output buffer test (3-state) - HSPICE IBIS syntax
XOUT PAD_OUT VCC VSS IN_SIG ENABLE IBIS_MODEL=AT16245_OUT IBIS_FILE='ibis/at16245.ibs'

* Analysis options
.option post=2 accurate=1 gmin=1e-15
.option reltol=1e-6 abstol=1e-12 vntol=1e-6

* Temperature and process corners
.temp 25
.param supply_voltage=5.0

* Transient analysis
.tran 0.1n 50n

* Print statements
.print tran v(IN_SIG) v(PAD_IN) v(PAD_OUT) v(PAD_RCV) v(ENABLE)
.print tran i(VDD) i(VSS)

* AC analysis for frequency response
.ac dec 100 1MEG 1G
.print ac vdb(PAD_OUT) vp(PAD_OUT)

* DC sweep for I-V characteristics
.dc VIN_TEST 0 5 0.1
.print dc v(PAD_IN) v(PAD_OUT)

* Measurement statements
.measure tran tpd_lh trig v(IN_SIG) val=2.5 rise=1 targ v(PAD_OUT) val=2.5 rise=1
.measure tran tpd_hl trig v(IN_SIG) val=2.5 fall=1 targ v(PAD_OUT) val=2.5 fall=1
.measure tran tr_out trig v(PAD_OUT) val=1.25 rise=1 targ v(PAD_OUT) val=3.75 rise=1
.measure tran tf_out trig v(PAD_OUT) val=3.75 fall=1 targ v(PAD_OUT) val=1.25 fall=1

* Power measurements
.measure tran avg_power avg power from=10n to=40n
.measure tran peak_current max i(VDD) from=0 to=50n

.end
