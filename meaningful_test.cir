* Meaningful IBIS Model Test - Shows actual buffer behavior
.title IBIS Buffer Switching Test

* Include IBIS subcircuits
.include at16245_in.sp
.include at16245_out.sp

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals
* Input signal: 0->5V step at 5ns, 5V->0V step at 25ns
VIN IN_SIG 0 PWL(0 0 5n 0 5.1n 5 25n 5 25.1n 0 50n 0)

* Enable signal: always enabled
VEN ENABLE 0 DC 5.0

* Input buffer test signal
VIN_TEST PAD_IN 0 PWL(0 0 10n 0 10.1n 5 30n 5 30.1n 0 50n 0)

* Realistic loads
RLOAD PAD_OUT 0 50
CLOAD PAD_OUT 0 10pF

* Transmission line to receiver
RTLINE PAD_OUT PAD_MID 25
LTLINE PAD_MID PAD_RCV 5nH
RRCV PAD_RCV 0 50
CRCV PAD_RCV 0 5pF

* Reference nodes for waveform outputs
RRISE PAD_RISE 0 1MEG
RFALL PAD_FALL 0 1MEG

* Instantiate IBIS models
* Input buffer
XIN PAD_IN VCC VSS AT16245_IN

* Output buffer (corrected port order based on subcircuit definition)
XOUT IN_SIG VCC VSS PAD_OUT ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Analysis
.tran 0.1n 50n

* Output key voltages at specific times
.print tran v(IN_SIG) v(PAD_OUT) v(PAD_RCV) v(PAD_IN) v(ENABLE)

* Measure propagation delays
.measure tran tpd_lh trig v(IN_SIG) val=2.5 rise=1 targ v(PAD_OUT) val=2.5 rise=1
.measure tran tpd_hl trig v(IN_SIG) val=2.5 fall=1 targ v(PAD_OUT) val=2.5 fall=1

* Measure rise/fall times
.measure tran tr_out trig v(PAD_OUT) val=1.25 rise=1 targ v(PAD_OUT) val=3.75 rise=1
.measure tran tf_out trig v(PAD_OUT) val=3.75 fall=1 targ v(PAD_OUT) val=1.25 fall=1

* Measure output voltage levels
.measure tran vol_out min v(PAD_OUT) from=30n to=40n
.measure tran voh_out max v(PAD_OUT) from=10n to=20n

.end
