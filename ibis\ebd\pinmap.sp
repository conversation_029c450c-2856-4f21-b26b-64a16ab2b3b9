**********************************************************
*    Analysis And Options
**********************************************************

.op
.tran 10p 30n

.option post=2 probe 
**********************************************************
*    Stimulus
**********************************************************

Vin cmp1_5_i 0 0V pulse ( 0V 3V 2n 0.1n 0.1n 7.5n 15n )

Vpu cmp1_1 0 2.5v
vpc cmp1_2 0 2.5v
vpd cmp1_3 0 0v
vgc cmp1_4 0 0

**********************************************************
*    Rload
**********************************************************

Rd1 ebd1_a3 0 50
Rd2 ebd1_a4 0 50

**********************************************************
*    Define Component
**********************************************************
.ibis cmp1
+ file = 'pinmap.ibs'
+ component = 'Component1'
+ hsp_ver = 2003.3
+ package = 0

*********************************************************
*   Define EBD
*********************************************************

.ebd ebd1
+ file = 'pinmap.ebd'
+ model = 'Board1'
+ component = 'cmp1:u21'

********************************************************
*  Output
********************************************************

.probe tran
+ cmp1_5_out = v(cmp1_5)      $ buf_5(Output): cmp1_5
+ cmp1_5_in  = v(cmp1_5_i)    $ no package , so the out node is cmp1_5, 
                              $ not cmp1_5_o

+ ebd1_a3    = v(ebd1_a3)
+ ebd1_a4    = v(ebd1_a4)

.end
