.Title This is a test case for ICM with bga_example.icm
* To illustrate the usage of .icm command
* YuWang,May,12,2004
*
* external node naming rules
* "icm_name" + "_" + "pin/node map name"+"_"+"pin name"
* Where "icm_name" is defined by ".icm" statement, and "pin name" is 
* defined within [ICM Pin Map] and [ICM Node Map] keywords in ICM file. 
* All pin names of a ICM model will be automatically created internally 
* by HSPICE, so users need not to spend time to create more nodes for 
* ICM model, they just use these node name defined by HSPICE internally

* File:  bga_example.icm
* Model: diff-two-pair-short-TRL

.icm bga_example
+ file = "bga_example.icm"
+ model = "diff-two-pair-short-TRL"

* ICM Node Map
* [ICM Node Map] Die_side
* | pin node name
* 1 A1 Die_Pair1_P
* 2 A2 Die_Pair1_N
* 3 A3 Die_Pair2_P
* 4 A4 Die_Pair2_N

* [ICM Node Map] Ball_side
* | pin node name
* 1 B1 Ball_Pair1_P
* 2 B2 Ball_Pair1_N
* 3 B3 Ball_Pair2_P
* 4 B4 Ball_Pair2_N

* Die side
.param lo=0 hi=1 td=0 tr=0.5n tf=0.5n th=3n tp=7n
V_Die_1_p bga_example_Die_side_1 0 pulse(lo hi td tr tf th tp)
$V_Die_1_n bga_example_Die_side_2 0 pulse(hi lo td tr tf th tp)
$V_Die_2_p bga_example_Die_side_3 0 pulse(lo hi td tr tf th tp)
$V_Di2_2_n bga_example_Die_side_4 0 pulse(hi lo td tr tf th tp)

* Ball side
.param Ref_impedance=50
R_Ball_1_p bga_example_Ball_side_1 0 Ref_impedance
R_Ball_1_n bga_example_Ball_side_2 0 Ref_impedance
R_Ball_2_p bga_example_Ball_side_3 0 Ref_impedance
R_Ball_2_n bga_example_Ball_side_4 0 Ref_impedance


.Tran 10p 14n
.options post list
.end

