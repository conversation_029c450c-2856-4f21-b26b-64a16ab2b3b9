* ========================================
* Transient Simulation for DDR3L_DQ48_NO_ODT
* ========================================

* 电源
VCC VCC 0 DC 1.35V   ; VCC min=1.27, typ=1.35, max=1.42
VSS VSS 0 DC 0V

* 输入信号：OUT 数据（例如方波）
*VIN T 0 PWL(0 0V 2n 1.35V 4n 0V 6n -1.35V 15n 1.35V 30n -1.35V 70n 1.35V 100n 1.35V)  ; 1GHz 数据切换
VIN T 0 PULSE(0 1.35V 0 1n 1n 3n 10n)
* 使能信号：E（高电平使能输出）
*VEN E 0 PWL(0 0V 1n -1.35V 13n 1.35V 40n -1.35V 60n 1V 100n 1.35V )  ; 在 11ns 时使能
VEN E 0 PULSE(0 1V 0 1n 1n 3n 10n)
* 负载：50Ω 到地（IBIS 规定）
RLOAD IO VSS 50
*CLOAD IO VSS 10pF

* 调用 IBIS 转换的子电路
X1 T VCC VSS IO VCC VSS E PAD_RISE PAD_FALL DDR3L_DQ48_NO_ODT

* 不连接 PAD_RISE 和 PAD_FALL（仅用于内部参考）
* 如果不使用，可以浮空（ngspice 允许）

* 瞬态分析
.tran 10p 100n  ; 步长 10ps，总时间 50ns

* 控制块：运行仿真并绘图
.control
run
*plot V(DIE) V(en_pu) V(en_pd)
*plot V(IO) V(E) V(T)
plot V(IO)
.endc

* 包含子电路定义（将你的 IBIS 转换代码放在这里）
.include "sa.sp"  ; 假设子电路保存在外部文件
* 或者直接粘贴 .SUBCKT ... .ENDS 在此处

.end