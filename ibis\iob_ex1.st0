****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: iob_ex1.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   chenqi               HOSTNAME: DESKTOP-K7C3CEV
lic: HOSTID: 000c29e08aa6         PID:      26900
lic: Using FLEXlm license file:
lic: 27000@DESKTOP-K7C3CEV
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-K7C3CEV
lic:

 init: begin read circuit files, cpu clock= 2.48E+00
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/behav
                         e/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/AD/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/XILIN
                         X/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/TLINE
                         /
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/TI/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/SIGNE
                         T/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/PCI/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/LIN_T
                         ECH/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/PET/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/DI0/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/COMLI
                         NE/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/BURR_
                         BRM/
       option search = E:/hspice2019/Hspice_P-2019.06-SP1-1/parts/BJT/
       option post
 init: end read circuit files, cpu clock= 2.48E+00 peak memory=      68 mb
 init: begin check errors, cpu clock= 2.48E+00
 init: end check errors, cpu clock= 2.54E+00 peak memory=      68 mb
 init: begin setup matrix, pivot=     0 cpu clock= 2.54E+00
       establish matrix -- done, cpu clock= 2.54E+00 peak memory=      68 mb
       re-order matrix -- done, cpu clock= 2.54E+00 peak memory=      68 mb
 init: end setup matrix, cpu clock= 2.54E+00 peak memory=      68 mb
 dcop: begin dcop, cpu clock= 2.54E+00
 dcop: end dcop, cpu clock= 2.55E+00 peak memory=      68 mb tot_iter=       5
 output: E:\hspice2019\Hspice_P-2019.06-SP1-1\Demo\hspice\ibis\iob_ex1.tr0
 sweep: tran tran0    begin, stop_t=  7.00E-08 #sweeps=1401 cpu clock= 2.55E+00
 tran: time= 7.0349E-09 tot_iter=     130 conv_iter=      65 cpu clock= 2.56E+00
 tran: time= 1.4136E-08 tot_iter=     231 conv_iter=     114 cpu clock= 2.56E+00
 tran: time= 2.1000E-08 tot_iter=     296 conv_iter=     143 cpu clock= 2.56E+00
 tran: time= 2.8461E-08 tot_iter=     437 conv_iter=     210 cpu clock= 2.56E+00
 tran: time= 3.5107E-08 tot_iter=     547 conv_iter=     264 cpu clock= 2.56E+00
 tran: time= 4.2000E-08 tot_iter=     600 conv_iter=     286 cpu clock= 2.56E+00
 tran: time= 4.9168E-08 tot_iter=     780 conv_iter=     369 cpu clock= 2.56E+00
 tran: time= 5.6094E-08 tot_iter=     925 conv_iter=     430 cpu clock= 2.56E+00
 tran: time= 6.3224E-08 tot_iter=    1040 conv_iter=     484 cpu clock= 2.57E+00
 tran: time= 7.0000E-08 tot_iter=    1114 conv_iter=     517 cpu clock= 2.57E+00
 sweep: tran tran0    end, cpu clock= 2.57E+00 peak memory=      68 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
