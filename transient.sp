* Example SPICE Netlist for AT16245_OUT Transient Simulation
* Using IBIS-derived subcircuit

* Include the subcircuit definition
.INC test_cmdline_input.sp   ; Replace with actual path/filename if needed

* Power supply
VCC VCC 0 DC 5V

* Ground
VSS VSS 0 DC 0V

* Input stimulus for OUTPUT VALUE (simulate logic driving the buffer input)
* Here, we assume 'OUT' is the data input to the buffer (from internal logic)
Vin OUT 0 PWL(0 0V  10ns 0V  11ns 5V  20ns 5V  21ns 0V)  ; Toggle OUT at ~10ns and 20ns

* Enable signal: active high (assumed from model)
Ven ENABLE 0 PWL(0 0V  5ns 0V  6ns 5V  30ns 5V)  ; Enable buffer after 6ns

* Load capacitance (typical test load)
CLOAD PAD VSS 50fF

* Termination resistor (optional, e.g., 50 Ohm to ground or mid-supply)
RTERM PAD VSS 100k ; High-Z termination; can be 50 Ohm if needed

* Call the subcircuit
X1 PAD VCC VSS OUT ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Reference voltage sources for rise/fall measurement (fixed fixtures as per IBIS)
* These are used by the model for calibration — just connect them as specified

* Optional: Monitor internal nodes
*C_comp_probe OUT VSS 3.4pF ; Already inside model, just for understanding

*================== Simulations ===================

* Transient analysis: simulate from 0 to 30ns, step 10ps
.TRAN 10ps 30ns

* Optional: Operating point to check initial state
.OP

* Output voltage probes
.PLOT TRAN V(PAD) V(OUT) V(ENABLE)
.PROBE                    ; Enable waveform viewing in simulators like LTspice
.control
run
.endc
* End of simulation
.END