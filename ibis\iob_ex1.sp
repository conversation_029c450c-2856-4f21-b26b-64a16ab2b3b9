* IBIS Buffer Test
.option post
.tran 0.05n 70n
*
* input source
v1 in  0 pulse ( 0V 1V 1n 1n 1n 9n 20n )
r1 in  in1 50
* tristate enable
v5 enable  0 1V 

* transmission line
wline1 n1 0 n2 0 RLGCmodel=pcb N=1 L=0.3

* IBIS buffers
b1 nd_pc nd_gc n2 out_of_in
+ file = 'at16245.ibs'
+ model = 'AT16245_IN'

b4 nd_pu nd_pd n1 in1 enable
+ file = 'at16245.ibs'
+ model = 'AT16245_OUT'
+ ramp_fwf=0 ramp_rwf=0

* load
rload n2 0 50

* RLCG parameters for W-element
.model pcb w modeltype=rlgc n=1
+L0=3.94266e-7
+C0=1.12727e-10
+R0=5.7739
+G0=0
+Rs=0.00141445
+Gd=0

.end
