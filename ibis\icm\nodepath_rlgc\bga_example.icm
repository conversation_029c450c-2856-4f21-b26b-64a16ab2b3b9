|
|**************************************************************************
| The following example corresponds to a BGA wirebond package 
|**************************************************************************
[Begin Header]
[ICM Ver]    1.0
[File Rev]   1.1
[File Name]  bga_example.icm
[Date]       10/15/2003
[Source]     From SPICE model data
[Notes]      This is an example wirebond BGA package model.
[Disclaimer] This model is provided as an example only.
[Copyright]  ---
[Support]    http://www.yourcompanyhere.com
[Redistribution] Yes
[End Header]
|
|**************************************************************************
|
[Begin ICM Family]  Wirebond_Package
[Manufacturer]      Noname
[ICM Family Description] A wirebond BGA package design
|
|**************************************************************************
|
[ICM Model List]
| Name                   Mating         Min_Slew_Time    Image
|----------------------------------------------------------------------------
diff-two-pair-short-TRL   Mated          1000ps   
diff-two-pair-mid-TRL     Mated          1000ps   
diff-two-pair-long-TRL    Mated          1000ps   
|
|          wirebond   TRL   via     ball
|Die_side >--------< >---< >---< >--------< Ball_side
|                               |
|                               |platingbar   
|
|**************************************************************************
|Units below assumed to be meters
|
[Begin ICM Model] diff-two-pair-short-TRL
ICM_model_type MLM
[Nodal Path Description]
Model_nodemap Die_side
N_section (A1 A2 A3 A4  11 12 13 14 ) Mult=1     wirebond
N_section (11 12 13 14  21 22 23 24 ) Len=1.0e-3 TRL
N_section (21 22        31 32       ) Mult=1     via
N_section (      23 24        33 34 ) Mult=1     via
N_section (31 32        x1 x2       ) Len=1.0e-3 platingbar
N_section (      33 34        x3 x4 ) Len=1.0e-3 platingbar
N_section (31 32        B1 B2       ) Mult=1     ball
N_section (      33 34        B3 B4 ) Mult=1     ball
Model_nodemap Ball_side
[End ICM Model]
|
[Begin ICM Model] diff-two-pair-mid-TRL
ICM_model_type MLM
[Nodal Path Description]
Model_nodemap Die_side
N_section (A1 A2 A3 A4  11 12 13 14 ) Mult=1     wirebond
N_section (11 12 13 14  21 22 23 24 ) Len=2.5e-3 TRL
N_section (21 22        31 32       ) Mult=1     via
N_section (      23 24        33 34 ) Mult=1     via
N_section (31 32        x1 x2       ) Len=1.0e-3 platingbar
N_section (      33 34        x3 x4 ) Len=1.0e-3 platingbar
N_section (31 32        B1 B2       ) Mult=1     ball
N_section (      33 34        B3 B4 ) Mult=1     ball
Model_nodemap Ball_side
[End ICM Model]
|
[Begin ICM Model] diff-two-pair-long-TRL
ICM_model_type MLM
[Nodal Path Description]
Model_nodemap Die_side
N_section (A1 A2 A3 A4  11 12 13 14 ) Mult=1     wirebond
N_section (11 12 13 14  21 22 23 24 ) Len=4.0e-3 TRL
N_section (21 22        31 32       ) Mult=1     via
N_section (      23 24        33 34 ) Mult=1     via
N_section (31 32        x1 x2       ) Len=1.0e-3 platingbar
N_section (      33 34        x3 x4 ) Len=1.0e-3 platingbar
N_section (31 32        B1 B2       ) Mult=1     ball
N_section (      33 34        B3 B4 ) Mult=1     ball
Model_nodemap Ball_side
[End ICM Model]
|
|**************************************************************************
|
[ICM Node Map] Die_side
| pin node name
1 A1 Die_Pair1_P
2 A2 Die_Pair1_N
3 A3 Die_Pair2_P
4 A4 Die_Pair2_N

[ICM Node Map] Ball_side
| pin node name
1 B1 Ball_Pair1_P
2 B2 Ball_Pair1_N
3 B3 Ball_Pair2_P
4 B4 Ball_Pair2_N

[End ICM Family]
|
|**************************************************************************
[Begin ICM Section] TRL
[Derivation Method] Distributed
| Note - resistance and conductance data taken at DC
[Resistance Matrix] Full_matrix
[Row] 1
12.88360411759953 0.1122120358629589 0.1122120358629535 0.1122120358629596
[Row] 2
12.88360411759959 0.1122120358629578 0.1122120358629697
[Row] 3
12.88360411759938 0.1122120358630265
[Row] 4
12.88360411759949

[Inductance Matrix] Full_matrix
[Row] 1
  4.762471117800315e-007  1.574556825167307e-007  4.166419413304077e-008  2.533102103479589e-008
[Row] 2
  4.746270568634125e-007  7.829217743956544e-008  4.167732136295897e-008
[Row] 3
   4.74695722999582e-007  1.575422041099422e-007
[Row] 4
  4.764080626393532e-007

[Capacitance Matrix] Full_matrix
[Row] 1
1.13724568263412e-010 -3.699327364720182e-011  -3.195922952611326e-012  -1.703330485507162e-012
[Row] 2
1.161253452991748e-010  -1.420237001727346e-011 -3.194945464961537e-012
[Row] 3
1.160998680089507e-010 -3.698488552069936e-011
[Row] 4
1.135869774794572e-010

[Conductance Matrix] Diagonal_matrix
0
0
0
0
[End ICM Section] TRL
|**************************************************************************
|
[Begin ICM Section] platingbar
[Derivation Method] Distributed
| Note - resistance and conductance data taken at DC
[Resistance Matrix] Full_matrix
[Row] 1
6.447272395548234 0.06157635467979937
[Row] 2
6.447272395547882

[Inductance Matrix] Full_matrix
[Row] 1
3.875774956956585e-007   1.453875900301228e-007
[Row] 2
3.876372566358464e-007

[Capacitance Matrix] Full_matrix
[Row] 1
1.228751177567252e-010 -4.55112340759e-011
[Row] 2
1.228294743230235e-010

[Conductance Matrix] Diagonal_matrix
0
0
[End ICM Section] platingbar
|**************************************************************************
[Begin ICM Section] ball
[Derivation Method] Lumped
| Note - resistance and conductance data taken at DC
[Resistance Matrix] Full_matrix
[Row] 1
0.00122974     5.92605e-005 
[Row] 2
0.00121302 

[Inductance Matrix] Full_matrix
[Row] 1
3.91849e-011     4.81629e-012 
[Row] 2
4.0174e-011 

[Capacitance Matrix] Full_matrix
[Row] 1
3.99986e-014    -5.64816e-015 
[Row] 2
3.84177e-014 

[Conductance Matrix] Diagonal_matrix
0
0
[End ICM Section] ball
|**************************************************************************
[Begin ICM Section] wirebond
[Derivation Method] Lumped
| Note - resistance and conductance data taken at DC
[Resistance Matrix] Full_matrix
[Row] 1
0.357348   0.0153167 0.000203431 0.0
[Row] 2
0.360486  0.00520121 0.000603478 
[Row] 3
0.358009   0.0157098 
[Row] 4
0.356194 

[Inductance Matrix] Full_matrix
[Row] 1
3.12277e-009     1.01577e-009     3.05886e-010     2.22836e-010 
[Row] 2
3.02065e-009     4.47214e-010     2.93897e-010 
[Row] 3
2.99636e-009     9.45824e-010 
[Row] 4
3.01787e-009 

[Capacitance Matrix] Full_matrix
[Row] 1
4.03601e-013    -1.28769e-013    -1.50353e-014    -8.75617e-015 
[Row] 2
4.19554e-013    -3.78802e-014    -1.42581e-014 
[Row] 3
4.16062e-013    -1.22606e-013 
[Row] 4
4.06634e-013 

[Conductance Matrix] Diagonal_matrix
0
0
0
0
[End ICM Section] wirebond
|**************************************************************************
[Begin ICM Section] via
[Derivation Method] Lumped
| Note - resistance and conductance data taken at DC
[Resistance Matrix] Full_matrix
[Row] 1
0.00301846 0.000640491 
[Row] 2
0.00304867 

[Inductance Matrix] Full_matrix
[Row] 1
6.42506e-011     2.56352e-011 
[Row] 2
5.96354e-011 

[Capacitance Matrix] Full_matrix
[Row] 1
4.81705e-013    -8.01111e-014 
[Row] 2
4.89624e-013 

[Conductance Matrix] Diagonal_matrix
0
0
[End ICM Section] via
|**************************************************************************
|
[End]
