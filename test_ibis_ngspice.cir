* NGSPICE Test Circuit for IBIS Model Conversion
* Tests AT16245 IBIS models converted to SPICE subcircuits
* Author: IBIS2SPICE Converter Test
* Date: 2025-01-01

.title IBIS Model Test - AT16245 Input/Output Buffers

* Include converted IBIS subcircuits
.include at16245_in.sp
.include at16245_out.sp

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals
* Input test signal - step from 0V to 5V
VIN_TEST IN_SIG 0 PULSE(0 5 1n 1n 1n 10n 20n)

* Enable signal for 3-state buffer
VEN ENABLE 0 PULSE(0 5 0.5n 0.1n 0.1n 15n 30n)

* Load resistors and capacitors
RLOAD_IN PAD_IN 0 1MEG
CLOAD_IN PAD_IN 0 10pF

RLOAD_OUT PAD_OUT 0 50
CLOAD_OUT PAD_OUT 0 5pF

* Transmission line (optional)
TLINE PAD_OUT PAD_RCV 0 0 Z0=50 TD=1n

* Receiver load
RRCV PAD_RCV 0 50
CRCV PAD_RCV 0 2pF

* Reference nodes for waveform outputs
RRISE PAD_RISE 0 1MEG
RFALL PAD_FALL 0 1MEG

* Instantiate IBIS models
* Input buffer test
XIN PAD_IN VCC VSS AT16245_IN

* Output buffer test (3-state) - note: AT16245_OUT has 7 ports
XOUT PAD_OUT VCC VSS IN_SIG ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Analysis setup
.control
* Transient analysis
tran 0.1n 50n

* Print key voltages and currents
print v(IN_SIG) v(PAD_IN) v(PAD_OUT) v(PAD_RCV) v(ENABLE)
print i(VDD) i(VSS)

* Plot waveforms
plot v(IN_SIG) v(PAD_OUT) v(PAD_RCV) v(ENABLE) title 'IBIS Model Test Waveforms'
plot i(VDD) title 'Supply Current'

* Save results
write test_ibis_results.raw v(IN_SIG) v(PAD_IN) v(PAD_OUT) v(PAD_RCV) v(ENABLE) i(VDD) i(VSS)

* AC analysis for frequency response
ac dec 100 1MEG 1G
plot vdb(PAD_OUT) vp(PAD_OUT) title 'Output Frequency Response'

* DC sweep for I-V characteristics
dc VIN_TEST 0 5 0.1
plot v(PAD_IN) v(PAD_OUT) title 'DC Transfer Characteristics'

echo "NGSPICE IBIS Model Test Completed"
.endc

* SPICE model parameters for compatibility
.model NMOS NMOS(Level=1 VTO=0.7 KP=120u GAMMA=0.4 PHI=0.65)
.model PMOS PMOS(Level=1 VTO=-0.7 KP=40u GAMMA=0.4 PHI=0.65)

.end
