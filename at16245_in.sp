* SPICE Subcircuit generated from IBIS model
* Model: AT16245_IN
* Model Type: input
* Generated by ibis2spice.py

* Unknown model type: input

.SUBCKT AT16245_IN PAD VCC VSS
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground

R_pkg DIE OUT 0.2Ohm
L_pkg DIE OUT 7.000000e-09nH
C_pkg OUT VSS 1.500000e-12pF
.PARAM VHI=2.0
.PARAM VLO=0.8
* Input/Output Capacitance:
C_comp PAD VSS 3.400000e-12F

* Voltage Range: [5.0, 4.75, 5.25]

* Temperature Range: [25.0, 0.0, 85.0]

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(PAD,VSS)} = (-5,-4.269) (-4.9,-4.17) (-4.8,-4.071) (-4.7,-3.972) (-4.6,-3.874)
+ (-4.5,-3.775) (-4.4,-3.676) (-4.3,-3.577) (-4.2,-3.478) (-4.1,-3.38)
+ (-4,-3.281) (-3.9,-3.182) (-3.8,-3.083) (-3.7,-2.985) (-3.6,-2.886)
+ (-3.5,-2.787) (-3.4,-2.689) (-3.3,-2.59) (-3.2,-2.492) (-3.1,-2.393)
+ (-3,-2.295) (-2.9,-2.196) (-2.8,-2.098) (-2.7,-2) (-2.6,-1.902)
+ (-2.5,-1.804) (-2.4,-1.705) (-2.3,-1.607) (-2.2,-1.51) (-2.1,-1.412)
+ (-2,-1.314) (-1.9,-1.217) (-1.8,-1.119) (-1.7,-1.022) (-1.6,-0.9255)
+ (-1.5,-0.8288) (-1.4,-0.7325) (-1.3,-0.6366) (-1.2,-0.5412) (-1.1,-0.4467)
+ (-1,-0.3532) (-0.9,-0.2614) (-0.8,-0.1726) (-0.7,-0.08983) (-0.6,-0.02419)
+ (-0.5,-0.001292) (-0.4,-5.192000e-05) (-0.3,-1.779000e-05) (-0.2,-1.129000e-05) (-0.1,-5.577000e-06)
+ (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (-5,4.217) (-4.9,4.119) (-4.8,4.02) (-4.7,3.922) (-4.6,3.824)
+ (-4.5,3.726) (-4.4,3.628) (-4.3,3.53) (-4.2,3.432) (-4.1,3.333)
+ (-4,3.235) (-3.9,3.137) (-3.8,3.039) (-3.7,2.941) (-3.6,2.843)
+ (-3.5,2.746) (-3.4,2.648) (-3.3,2.55) (-3.2,2.452) (-3.1,2.354)
+ (-3,2.257) (-2.9,2.159) (-2.8,2.061) (-2.7,1.964) (-2.6,1.866)
+ (-2.5,1.769) (-2.4,1.671) (-2.3,1.574) (-2.2,1.477) (-2.1,1.38)
+ (-2,1.283) (-1.9,1.186) (-1.8,1.089) (-1.7,0.9933) (-1.6,0.8971)
+ (-1.5,0.8012) (-1.4,0.7056) (-1.3,0.6105) (-1.2,0.5159) (-1.1,0.4222)
+ (-1,0.3297) (-0.9,0.2391) (-0.8,0.1518) (-0.7,0.07194) (-0.6,0.01426)
+ (-0.5,5.020000e-04) (-0.4,1.075000e-05) (-0.3,0) (0,0)

.ENDS
