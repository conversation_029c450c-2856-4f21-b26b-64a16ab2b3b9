* SPICE Subcircuit generated from IBIS model
* Model: TOP_MODEL_S_CLMP
* Model Type: i/o
* Generated by ibis2spice.py

* Unknown model type: i/o

.SUBCKT TOP_MODEL_S_CLMP PAD VCC VSS OUT ENABLE
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* OUT      - Output signal
* ENABLE   - Enable signal (for 3-state/I/O models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

R_pkg DIE OUT 0.2Ohm
L_pkg DIE OUT 4.320000e-09nH
C_pkg OUT VSS 3.800000e-13pF
.PARAM VTH=0.0
* Input/Output Capacitance:
C_comp PAD VSS 4.000000e-12F

* Voltage Range: [5.0, 4.5, 5.5]

* Temperature Range: [50.0, 100.0, 0.0]

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (-5,0) (-4.6,0.001) (-4.2,0.0014) (-3.8,0.0011) (-3.4,8.000000e-04)
+ (-3,7.000000e-04) (-2.6,4.000000e-04) (-2.2,2.000000e-04) (-1.8,-2.000000e-04) (-1.4,9.600000e-04)
+ (-1,-9.000000e-05) (-0.9,-0.00125) (-0.8,-0.002344) (-0.7,-0.003313) (-0.6,-0.003947)
+ (-0.5,-0.0039814) (-0.4,-0.0034549) (-0.3,-0.0027724) (-0.2,-0.0020821) (-0.1,-0.0013895)
+ (0,-6.899000e-04) (0.1,5.377000e-05) (0.2,0.0030355) (0.3,0.0491785) (0.4,0.1009)
+ (0.5,0.148) (0.6,0.187) (0.7,0.2063) (0.8,0.2093) (0.9,0.2104)
+ (1,0.2114) (1.1,0.2125) (1.2,0.2136) (1.3,0.2146) (1.4,0.2157)
+ (1.5,0.2167) (1.6,0.2178) (1.7,0.2188) (1.8,0.2198) (1.9,0.2208)
+ (2,0.2218) (2.1,0.2228) (2.2,0.2239) (2.3,0.2249) (2.4,0.2259)
+ (2.5,0.2269) (2.6,0.2278) (2.7,0.2288) (2.8,0.2298) (2.9,0.2308)
+ (3,0.2318) (3.1,0.2327) (3.2,0.2337) (3.3,0.2345) (3.4,0.2355)
+ (3.5,0.2364) (3.6,0.2373) (3.7,0.2382) (3.8,0.2391) (3.9,0.24)
+ (4,0.2409) (4.1,0.2417) (4.2,0.2426) (4.3,0.2434) (4.4,0.2442)
+ (4.5,0.245) (4.6,0.2458) (4.7,0.2466) (4.8,0.2474) (4.9,0.2481)
+ (5,0.2488) (5.1,0.2495) (5.2,0.2502) (5.3,0.2508) (5.4,0.2514)
+ (5.5,0.252) (5.6,0.2526) (5.7,0.2531) (5.8,0.2536) (5.9,0.2542)
+ (6,0.2547) (6.4,0.2568) (6.8,0.2588) (7.2,0.2607) (7.6,0.2626)
+ (8,0.2643) (8.4,0.266) (8.8,0.2677) (9.2,0.2694) (9.6,0.2711)
+ (9.9999,0.2727)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(PAD,VCC)} = (-4.9999,-5.100000e-12) (-4.6,-5.200000e-12) (-4.2,-5.300000e-12) (-3.8,-5.400000e-12) (-3.4,-5.400000e-12)
+ (-3,-5.550000e-12) (-2.6,-5.670000e-12) (-2.2,-5.780000e-12) (-1.8,-5.870000e-12) (-1.4,-5.950000e-12)
+ (-1,-6.000000e-12) (-0.9,-6.020000e-12) (-0.8,-6.030000e-12) (-0.7,-6.040000e-12) (-0.6,-6.050000e-12)
+ (-0.5,-6.060000e-12) (-0.4,-6.070000e-12) (-0.3,-6.090000e-12) (-0.2,-6.090000e-12) (-0.1,-6.120000e-12)
+ (0,-6.160000e-12) (0.1,5.725000e-11) (0.2,5.350000e-11) (0.3,4.808000e-11) (0.4,3.184000e-11)
+ (0.5,-5.728000e-11) (0.6,-6.002000e-10) (0.7,-4.028000e-09) (0.8,-2.594000e-08) (0.9,-1.631000e-07)
+ (1,-9.728000e-07) (1.1,-5.180000e-06) (1.2,-2.689000e-05) (1.3,-1.262000e-04) (1.4,-6.359000e-04)
+ (1.5,-0.003167) (1.6,-0.01241) (1.7,-0.0258) (1.8,-0.03243) (1.9,-0.0382)
+ (2,-0.0437) (2.1,-0.04902) (2.2,-0.05416) (2.3,-0.05906) (2.4,-0.06373)
+ (2.5,-0.06819) (2.6,-0.07252) (2.7,-0.07676) (2.8,-0.08094) (2.9,-0.08508)
+ (3,-0.08918) (3.1,-0.09326) (3.2,-0.09733) (3.3,-0.1014) (3.4,-0.105)
+ (3.5,-0.1089) (3.6,-0.1129) (3.7,-0.117) (3.8,-0.121) (3.9,-0.125)
+ (4,-0.129) (4.1,-0.1331) (4.2,-0.1371) (4.3,-0.1411) (4.4,-0.1451)
+ (4.5,-0.1491) (4.6,-0.1531) (4.7,-0.1572) (4.8,-0.1612) (4.9,-0.1652)
+ (5,-0.1692) (5.1,-0.1732) (5.2,-0.1772) (5.3,-0.1812) (5.4,-0.1852)
+ (5.5,-0.1893) (5.6,-0.194) (5.7,-0.2003) (5.8,-0.209) (5.9,-0.2188)
+ (6,-0.229) (6.4,-0.2741) (6.8,-0.3477) (7.2,-0.4333) (7.6,-0.5228)
+ (8,-0.6147) (8.4,-0.7087) (8.8,-0.8044) (9.2,-0.9017) (9.6,-1.002)
+ (10,-1.106)

* Ramp Information
* Rising edge dV/dt: [1.6517, 9.122000000000001e-10]
* Falling edge dV/dt: [2.763, 6.367000000000001e-10]
* Load resistance: 50Ohm

.ENDS
