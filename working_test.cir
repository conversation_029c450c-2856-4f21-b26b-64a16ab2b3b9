* Working IBIS Model Test - Transient Analysis
.title Working IBIS Transient Test

* Include IBIS subcircuits
.include at16245_in.sp
.include at16245_out.sp

* Supply voltages
VDD VCC 0 DC 5.0
VSS VSS 0 DC 0.0

* Test signals - Digital pulse train
VIN IN_SIG 0 PULSE(0 5 1n 1n 1n 10n 20n)
VEN ENABLE 0 DC 5.0

* Input test signal for input buffer
VIN_TEST PAD_IN 0 PULSE(0 5 2n 1n 1n 8n 16n)

* Load resistors and capacitors
RLOAD PAD_OUT 0 50
CLOAD PAD_OUT 0 5pF

* Transmission line simulation
RTLINE PAD_OUT PAD_RCV 25
LTLINE PAD_OUT PAD_RCV 2nH
RRCV PAD_RCV 0 25

* Reference nodes for waveform outputs
RRISE PAD_RISE 0 1MEG
RFALL PAD_FALL 0 1MEG

* Instantiate models
XIN PAD_IN VCC VSS AT16245_IN
XOUT IN_SIG VCC VSS PAD_OUT ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Transient analysis
.tran 0.1n 50n

* Print statements
.print tran v(IN_SIG) v(PAD_OUT) v(PAD_IN) v(PAD_RCV) v(ENABLE)
.print tran i(VDD) i(VSS)

.end
