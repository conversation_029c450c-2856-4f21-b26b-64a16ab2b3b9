{
header: {
  IBIS Ver: '2.1'
  File Name: 'at16245.ibs'
  File Rev: '1.0'
  Date: 'Jan 15, 1996'
  Source: 'Atmel spice models'
  Notes: 'This ibis package is made for Atmel AT16245.
It includes following models:
AT16245_IN
AT16245_OUT'
  Disclaimer: 'This information is for modeling purpose only.'
  Copyright: 'Copyright 1996 by Atmel
************************************************************************
Component AT16245
************************************************************************'
},
Component: {
  AT16245: {
    Manufacturer: 'ATMEL'
    Package: {
      R_pkg: [
          '0.2'
          'None'
          'None'
        ]
      L_pkg: [
          '7.000000000000001e-09'
          'None'
          'None'
        ]
      C_pkg: [
          '1.5e-12'
          'None'
          'None'
        ]
    },
    Pin: {
      1: {
        signal_name: '1DIR'
        model_name: 'AT16245_IN'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      2: {
        signal_name: '1B1'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      3: {
        signal_name: '1B2'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      4: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      5: {
        signal_name: '1B3'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      6: {
        signal_name: '1B4'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      7: {
        signal_name: 'VCC'
        model_name: 'POWER'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      8: {
        signal_name: '1B5'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      9: {
        signal_name: '1B6'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      10: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      11: {
        signal_name: '1B7'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      12: {
        signal_name: '1B8'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      13: {
        signal_name: '2B1'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      14: {
        signal_name: '2B2'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      15: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      16: {
        signal_name: '2B3'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      17: {
        signal_name: '2B4'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      18: {
        signal_name: 'VCC'
        model_name: 'POWER'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      19: {
        signal_name: '2B5'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      20: {
        signal_name: '2B6'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      21: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      22: {
        signal_name: '2B7'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      23: {
        signal_name: '2B8'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      24: {
        signal_name: '2DIR'
        model_name: 'AT16245_IN'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      25: {
        signal_name: '2OE'
        model_name: 'AT16245_IN'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      26: {
        signal_name: '2A8'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      27: {
        signal_name: '2A7'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      28: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      29: {
        signal_name: '2A6'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      30: {
        signal_name: '2A5'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      31: {
        signal_name: 'VCC'
        model_name: 'POWER'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      32: {
        signal_name: '2A4'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      33: {
        signal_name: '2A3'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      34: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      35: {
        signal_name: '2A2'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      36: {
        signal_name: '2A1'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      37: {
        signal_name: '1A8'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      38: {
        signal_name: '1A7'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      39: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      40: {
        signal_name: '1A6'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      41: {
        signal_name: '1A5'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      42: {
        signal_name: 'VCC'
        model_name: 'POWER'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      43: {
        signal_name: '1A4'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      44: {
        signal_name: '1A3'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      45: {
        signal_name: 'GND'
        model_name: 'GND'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      46: {
        signal_name: '1A2'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      47: {
        signal_name: '1A1'
        model_name: 'AT16245_OUT'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
      48: {
        signal_name: '1OE'
        model_name: 'AT16245_IN'
        r_pin: 'None'
        l_pin: 'None'
        c_pin: 'None'
      },
    },
  },
},
Model: {
  AT16245_IN: {
    Temperature Range: [
        '25.0'
        '0.0'
        '85.0'
      ]
    Model_type: 'input'
    Polarity: 'non-inverting'
    Vinl: '0.8'
    Vinh: '2.0'
    C_comp: [
        '3.3999999999999996e-12'
        '2e-12'
        '6e-12'
      ]
    Voltage Range: [
        '5.0'
        '4.75'
        '5.25'
      ]
    GND Clamp: [
        '([-5.0, -4.9, -4.8, -4.7, -4.6, -4.5, -4.4, -4.3, -4.2, -4.1, -4.0, -3.9, -3.8, -3.7, -3.6, -3.5, -3.4, -3.3, -3.2, -3.1, -3.0, -2.9, -2.8, -2.7, -2.6, -2.5, -2.4, -2.3, -2.2, -2.1, -2.0, -1.9, -1.8, -1.7, -1.6, -1.5, -1.4, -1.3, -1.2, -1.1, -1.0, -0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3, -0.2, -0.1, 0.0, 5.0], [-4.269, -4.17, -4.071, -3.972, -3.874, -3.775, -3.676, -3.577, -3.478, -3.38, -3.281, -3.182, -3.083, -2.985, -2.886, -2.787, -2.689, -2.59, -2.492, -2.393, -2.295, -2.196, -2.098, -2.0, -1.902, -1.804, -1.705, -1.607, -1.51, -1.412, -1.314, -1.217, -1.119, -1.022, -0.9255, -0.8288, -0.7325, -0.6366, -0.5412, -0.4467, -0.3532, -0.2614, -0.1726, -0.08983, -0.02419, -0.001292, -5.192e-05, -1.779e-05, -1.129e-05, -5.577e-06, 0.0, 0.0])'
        'None'
        'None'
      ]
    POWER Clamp: [
        '([-5.0, -4.9, -4.8, -4.7, -4.6, -4.5, -4.4, -4.3, -4.2, -4.1, -4.0, -3.9, -3.8, -3.7, -3.6, -3.5, -3.4, -3.3, -3.2, -3.1, -3.0, -2.9, -2.8, -2.7, -2.6, -2.5, -2.4, -2.3, -2.2, -2.1, -2.0, -1.9, -1.8, -1.7, -1.6, -1.5, -1.4, -1.3, -1.2, -1.1, -1.0, -0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3, 0.0], [4.217, 4.119, 4.02, 3.922, 3.824, 3.726, 3.628, 3.53, 3.432, 3.333, 3.235, 3.137, 3.039, 2.941, 2.843, 2.746, 2.648, 2.55, 2.452, 2.354, 2.257, 2.159, 2.061, 1.964, 1.866, 1.769, 1.671, 1.574, 1.477, 1.38, 1.283, 1.186, 1.089, 0.9933, 0.8971, 0.8012, 0.7056, 0.6105, 0.5159, 0.4222, 0.3297, 0.2391, 0.1518, 0.07194, 0.01426, 0.000502, 1.075e-05, 0.0, 0.0])'
        'None'
        'None'
      ]
  },
  AT16245_OUT: {
    Temperature Range: [
        '25.0'
        '0.0'
        '85.0'
      ]
    Model_type: '3-state'
    Polarity: 'non-inverting'
    Vmeas: '1.6'
    Rref: '50.0'
    Vref: '0.0'
    C_comp: [
        '3.3999999999999996e-12'
        '2e-12'
        '6e-12'
      ]
    Voltage Range: [
        '5.0'
        '4.75'
        '5.25'
      ]
    GND Clamp Reference: [
        '0.0'
        '0.0'
        '0.0'
      ]
    Pulldown: [
        '([-5.0, -4.0, -3.0, -2.0, -1.0, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4, 5.6, 5.8, 6.0, 6.2, 6.4, 6.6, 6.8], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.006073, 0.01146, 0.01621, 0.02038, 0.024, 0.02711, 0.2301, 0.2496, 0.2662, 0.2797, 0.2919, 0.301, 0.3061, 0.309, 0.3111, 0.3127, 0.3141, 0.3152, 0.3163, 0.3172, 0.3181, 0.319, 0.3198, 0.3205, 0.3213, 0.3221, 0.3228, 0.3236, 0.324, 0.33, 0.33, 0.33, 0.33, 0.33])'
        'None'
        'None'
      ]
    Pullup: [
        '([-5.0, -4.8, -4.6, -4.4, -4.2, -4.0, -3.8, -3.6, -3.4, -3.2, -3.0, -2.8, -2.6, -2.4, -2.2, -2.0, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.54e-08, -1.787e-05, -0.0006737, -0.003947, -0.008954, -0.01495, -0.02163, -0.02883, -0.03642, -0.04432, -0.05257, -0.0571, -0.06629, -0.07559, -0.1051, -0.1143, -0.1276, -0.1575, -0.1714])'   
        'None'
        'None'
      ]
    GND Clamp: [
        '([-5.0, -4.8, -4.6, -4.4, -4.2, -4.0, -3.8, -3.6, -3.4, -3.2, -3.0, -2.8, -2.6, -2.4, -2.2, -2.0, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 5.0], [-4.227, -4.031, -3.834, -3.637, -3.441, -3.245, -3.048, -2.852, -2.656, -2.46, -2.265, -2.069, -1.874, -1.679, -1.484, -1.29, -1.096, -0.9043, -0.713, -0.5239, -0.3391, -0.1661, -0.05746, -0.03637, -1.129e-05, 0.0, 0.0])'
        'None'
        'None'
      ]
    POWER Clamp: [
        '([-5.0, -4.8, -4.6, -4.4, -4.2, -4.0, -3.8, -3.6, -3.4, -3.2, -3.0, -2.8, -2.6, -2.4, -2.2, -2.0, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.4, -0.2, 0.0], [0.4322, 0.4123, 0.3924, 0.3726, 0.3528, 0.3329, 0.3131, 0.2933, 0.2735, 0.2537, 0.234, 0.2142, 0.1945, 0.1748, 0.1551, 0.1355, 0.1159, 0.09641, 0.07701, 0.05778, 0.03882, 0.02049, 0.004438, 1.071e-05, 0.0, 0.0])'
        'None'
        'None'
      ]
    Ramp: {
      R_load: '50'
      dV/dt_r: [
          '1.56'
          '2.0000000000000003e-10'
        ]
      dV/dt_f: [
          '1.81'
          '4.0000000000000007e-10'
        ]
    },
    Rising Waveform: [
        {
        C_fixture: '0.0'
        L_fixture: '0'
        R_dut: '0'
        L_dut: '0'
        C_dut: '0'
        R_fixture: '50.0'
        V_fixture: [
            '0.0'
            'None'
            'None'
          ]
        waveform: [
            '([0.0, 2e-10, 4e-10, 6e-10, 8e-10, 1e-09, 1.2e-09, 1.4e-09, 1.6e-09, 1.8e-09, 2e-09, 2.2e-09, 2.4e-09, 2.6e-09, 2.8e-09, 3e-09, 3.2e-09, 3.4e-09, 3.6e-09, 3.8e-09, 4e-09, 4.2e-09, 4.4e-09, 4.6e-09, 4.8e-09, 5e-09, 5.2e-09, 5.4e-09, 5.6e-09, 5.8e-09, 6e-09, 6.2e-09, 6.4e-09, 6.6e-09, 6.8e-09, 7e-09, 7.2e-09, 7.4e-09, 7.6e-09, 7.8e-09, 8e-09, 8.2e-09, 8.4e-09, 8.6e-09, 8.8e-09, 9e-09, 9.2e-09, 9.4e-09, 9.6e-09, 9.8e-09, 1e-08, 1.02e-08, 1.04e-08, 1.06e-08, 1.08e-08, 1.1e-08, 1.12e-08, 1.14e-08, 1.16e-08, 1.18e-08, 1.2e-08, 1.22e-08, 1.24e-08, 1.26e-08, 1.28e-08, 1.3e-08, 1.32e-08, 1.34e-08, 1.36e-08, 1.38e-08, 1.4e-08, 1.42e-08, 1.44e-08, 1.46e-08, 1.48e-08, 1.5e-08, 1.52e-08, 1.54e-08, 1.56e-08, 1.58e-08, 1.6e-08, 1.62e-08, 1.64e-08, 1.66e-08, 1.68e-08, 1.7e-08, 1.72e-08, 1.74e-08, 1.76e-08, 1.78e-08, 1.8e-08, 1.82e-08, 1.84e-08, 1.88e-08, 1.9e-08, 1.92e-08, 1.94e-08, 1.96e-08, 1.98e-08, 2e-08], [0.0023, 0.533, 2.5259, 3.2881, 3.4445, 3.443, 3.3939, 3.2827, 3.0596, 2.8487, 2.6685, 2.5152, 2.3871, 2.2798, 2.1878, 2.1111, 2.0467, 1.993, 1.947, 1.9086, 1.8772, 1.8527, 1.8335, 1.8197, 1.8113, 1.8051, 1.8021, 1.8021, 1.8036, 1.8059, 1.8097, 1.8143, 1.8197, 1.8251, 1.8304, 1.8358, 1.8404, 1.845, 1.8496, 1.8542, 1.858, 1.8619, 1.8657, 1.8688, 1.8726, 1.8757, 1.878, 1.881, 1.8833, 1.8864, 1.8887, 1.891, 1.8925, 1.8948, 1.8964, 1.8987, 1.9002, 1.9017, 1.904, 1.9056, 1.9071, 1.9086, 1.9094, 1.9109, 1.9125, 1.9132, 1.9148, 1.9163, 1.9171, 1.9186, 1.9194, 1.9201, 1.9217, 1.9224, 1.9232, 1.9247, 1.9255, 1.9263, 1.927, 1.9278, 1.9286, 1.9293, 1.9301, 1.9309, 1.9316, 1.9324, 1.9332, 1.9339, 1.9347, 1.9355, 1.9362, 1.937, 1.9378, 1.9385, 1.9393, 1.9401, 1.9401, 1.9408, 1.9416, 1.9424])'
            'None'
            'None'
          ]
      },
      ]
    Falling Waveform: [
        {
        C_fixture: '0.0'
        L_fixture: '0'
        R_dut: '0'
        L_dut: '0'
        C_dut: '0'
        R_fixture: '50.0'
        V_fixture: [
            '5.0'
            'None'
            'None'
          ]
        waveform: [
            '([0.0, 1e-10, 3e-10, 4e-10, 5e-10, 6e-10, 7e-10, 8e-10, 9e-10, 1e-09, 1.1e-09, 1.2e-09, 1.3e-09, 1.4e-09, 1.5e-09, 1.6e-09, 1.7e-09, 1.8e-09, 1.9e-09, 2e-09, 2.1e-09, 2.2e-09, 2.3e-09, 2.4e-09, 2.5e-09, 2.6e-09, 2.7e-09, 2.8e-09, 2.9e-09, 3e-09, 3.1e-09, 3.2e-09, 3.3e-09, 3.4e-09, 3.5e-09, 3.6e-09, 3.7e-09, 3.8e-09, 3.9e-09, 4e-09, 4.1e-09, 4.2e-09, 4.3e-09, 4.4e-09, 4.5e-09, 4.6e-09, 4.7e-09, 4.8e-09, 4.9e-09, 5e-09, 5.1e-09, 5.2e-09, 5.3e-09, 5.4e-09, 5.5e-09, 5.6e-09, 5.7e-09, 5.8e-09, 5.9e-09, 6e-09, 6.1e-09, 6.2e-09, 6.3e-09, 6.4e-09, 6.5e-09, 6.6e-09, 6.7e-09, 6.8e-09, 6.9e-09, 7e-09, 7.1e-09, 7.2e-09, 7.3e-09, 7.4e-09, 7.5e-09, 7.6e-09, 7.7e-09, 7.8e-09, 7.9e-09, 8e-09, 8.1e-09, 8.2e-09, 8.3e-09, 8.4e-09, 8.5e-09, 8.6e-09, 8.7e-09, 8.8e-09, 8.9e-09, 9e-09, 9.1e-09, 9.2e-09, 9.3e-09, 9.4e-09, 9.5e-09, 9.6e-09, 9.7e-09, 9.8e-09, 9.9e-09, 1e-08], [4.9999, 4.9891, 4.8663, 4.4131, 3.8101, 3.1248, 2.4179, 1.8265, 1.4605, 1.246, 1.1113, 1.0201, 0.9584, 0.9199, 0.9022, 0.8945, 0.8898, 0.8877, 0.8881, 0.8928, 0.9041, 0.9294, 0.9837, 1.0765, 1.152, 1.2044, 1.2361, 1.257, 1.2703, 1.2775, 1.2807, 1.2815, 1.2811, 1.2803, 1.2796, 1.2792, 1.279, 1.2784, 1.2773, 1.276, 1.2749, 1.2741, 1.2736, 1.273, 1.2721, 1.2711, 1.2699, 1.2687, 1.2675, 1.2663, 1.2652, 1.2641, 1.263, 1.2621, 1.2611, 1.2603, 1.2595, 1.2589, 1.258, 1.2574, 1.2567, 1.2562, 1.2555, 1.2549, 1.2545, 1.254, 1.2535, 1.2531, 1.2526, 1.2522, 1.2518, 1.2514, 1.2512, 1.2508, 1.2505, 1.2501, 1.2498, 1.2495, 1.2493, 1.249, 1.2487, 1.2485, 1.2483, 1.2481, 1.2478, 1.2477, 1.2474, 1.2473, 1.247, 1.2468, 1.2466, 1.2464, 1.2463, 1.246, 1.2459, 1.2458, 1.2456, 1.2455, 1.2454, 1.2451])'
            'None'
            'None'
          ]
      },
      ]
  },
},
},