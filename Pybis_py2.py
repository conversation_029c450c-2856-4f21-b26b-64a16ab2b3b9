# module pybis.py
#
# Copyright (C) 2012 <PERSON> <<EMAIL>>
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.

import sys
import math
import copy
import re
from pyparsing import alphanums, alphas, CaselessKeyword, Dict, Each, Forward, Group, LineStart, LineEnd, Literal, NotAny, one_of, Optional, ParseException, ParserElement, ParseResults, printables, restOfLine, Token, Suppress, Word
from collections import OrderedDict
from numpy import zeros

__all__ = ["Range", "IBSParser", "PKGParser", "EBDParser", "dump"]

def ParseReal(val):
    """Parse an IBIS formatted number."""
    try:
        return float(val)
    except:
        ext = val.lstrip('+-**********.eE')
        e = 1
        if len(ext):
            val = val[0:-len(ext)]
            e = 'fpnum.kMGT'.find(ext[0])
            if e == -1:
                e = 5
            e = 10**((e - 5) * 3)
        # 'e' or 'E' can be a unit
        if val[-1] == 'e' or val[-1] == 'E':
            val = val[:-1]
        return float(val) * e

def in_range(lower, upper):
    """Throw an exception if a number is not in a range."""
    def func(n):
        if not lower <= n <= upper:
            raise Exception("'{0}' is not in range '{1}'".format(n, (lower, upper)))
        return n
    return func

positive = in_range(0, float("inf"))

class Real(Token):
    name = 'Real'
    """pyparse a Real."""
    def __init__(self, check=lambda f: f):
        super(Real, self).__init__()
        self.check = check

    def parseImpl(self, instring, loc, doActions=True):
        tokens = re.split(r"[^a-zA-Z0-9\.\-\+]+", instring[loc:], 1)
        try:
            return loc + len(tokens[0]), self.check(ParseReal(tokens[0]))
        except:
            raise ParseException(tokens[0], loc, "Could not parse float, '{0}'".format(tokens[0]))

class Integer(Token):
    """pyparse an Integer."""
    def __init__(self, check=lambda f: f):
        super(Integer, self).__init__()
        self.check = check

    def parseImpl(self, instring, loc, doActions=True):
        tokens = re.split(r"[^0-9\-\+]+", instring[loc:], 1)
        val = tokens[0]
        try:
            return loc + len(val), self.check(int(val))
        except:
            raise ParseException(tokens[0], loc, "Could not parse integer")

class NAReal(Real):
    """pyparse an "optional" number, gives 'None' for 'NA'."""
    def parseImpl(self, instring, loc, doActions=True):
        if instring[loc:2+loc] == "NA":
            return loc + 2, None
        return super(NAReal, self).parseImpl(instring, loc, doActions)

class Range(list):
    """A typ, min, max range object.
       [0-2] -                  Return raw values (0 = typ, 1 = min, 2 = max).
       (0-2) -                  Return typ for min or max if they are 'None'.
       (0-2, invert=False) -    Ensure max > min.
       (0-2, invert=True) -     Ensure mix > max.
       .typ, .min, .max -       Easy accessors for (0-2).
       .norm -                  Return version of object where max > min.
       .inv -                   return version of object where min > max.
    """
    def __getattr__(self, name):
        if name == "typ":
            return self[0]
        elif name == "min":
            return self(1)
        elif name == "max":
            return self(2)
        elif name == "norm":
            if self.min > self.max:
                return Range([self[0], self[2], self[1]])
            else:
                return self
        elif name == "inv":
            if self.min < self.max:
                return Range([self[0], self[2], self[1]])
            else:
                return self
        else:
            raise AttributeError(name)

    def __call__(self, n, invert=None):
        if n == 0:
            return self[0]
        elif n < 3:
            if invert is not None and invert == self.min < self.max:
                n = 3 - n
            return self[n] if self[n] is not None else self[0]
        else:
            raise Exception

class RealRange(Token):
    """pyparser for a line of 'count' Real tokens.
       'check' should raise Exception on invalid value.
    """
    def __init__(self, count=3, check=lambda f: f):
        self.count = count * 2 - 1
        self.check = check
        super(RealRange, self).__init__()

    def parseImpl(self, instring, loc, doActions=True):
        tokens = re.split(r"([^a-zA-Z0-9\.\-\+]+)", instring[loc:])
        ret = Range()
        intok = True
        for tok in tokens[:self.count]:
            if intok:
                if len(ret) and tok == "NA":
                    ret.append(None)
                else:
                    try:
                        ret.append(self.check(ParseReal(tok)))
                    except:
                        raise ParseException(tok, loc, "Count not parse float")
            intok = not intok
            loc += len(tok)
        return loc, [ret]

def RampRange():
    """pyparser for range of IBIS ramp values (See Section 6 - [Ramp])."""
    ramp_data = Group(Real(check=positive) + Suppress(Literal("/")) + Real(check=positive))
    ret = (ramp_data -
        (ramp_data | CaselessKeyword("NA")) -
        (ramp_data | CaselessKeyword("NA"))
    )
    def fin(tokens):
        for i, t in enumerate(tokens[1:]):
            if t == "NA": tokens[i + 1] = None
        return Range(tokens.asList())
    ret.set_parse_action(fin)
    return ret

def oneof(val):
    """pyparser for set of caseless keywords.
       All parsed values will be lowercase.
    """
    return one_of(val.lower(), caseless=True)

def orderedDict(tokenlist):
    """pyparse action to create and OrderedDict from a set of tokens."""
    ret = OrderedDict()
    for i, tok in enumerate(tokenlist):
        ret[tok[0]] = tok[1]
    return ret

class IBISNode(OrderedDict):
    """IBIS parse results object.
       This object typically represents a section with named children.
       Because most IBIS keywords are case insensitive and many even don't
       distinguish between and '_' and ' ', this class allows loose access to
       children. All children are stored by their 'pretty_name', such as
       "Number of Sections". When an access is made, it is translated to a
       'simple_name' by lower() and translating ' ' and '/' to '_', '+' to 'p',
       and '-' to 'n'. The pretty_name is then looked up in an internal dict.
       The pretty_name is then used to find the child.
       In addition to normal dict() accessors, '__getattribute__' can also be
       used. For example, 'obj["Vinl+"] and obj.vinlp both work.
    """
    # Python 2 compatible translation table
    def __init__(self, *args, **kwds):
        object.__setattr__(self, 'pretty_names', dict())
        super(IBISNode, self).__init__(*args, **kwds)

    @staticmethod
    def simple_name(name):
        # Python 2 compatible string translation
        trans_table = {ord('+'): 'p', ord('-'): 'n', ord(' '): '_', ord('/'): '_'}
        return name.translate(trans_table).lower()

    def pretty_name(self, name):
        return self.pretty_names[IBISNode.simple_name(name)]

    def __getattribute__(self, name):
        try:
            return OrderedDict.__getattribute__(self, name)
        except AttributeError:
            try:
                return self[name]
            except KeyError:
                raise AttributeError(name)

    def __setattr__(self, name, value):
        try:
            pretty = self.pretty_name(name)
            OrderedDict.__setitem__(self, pretty, value)
        except KeyError:
            OrderedDict.__setattr__(self, name, value)

    def __getitem__(self, name):
        return OrderedDict.__getitem__(self, self.pretty_name(name))

    def __setitem__(self, name, value):
        simple = IBISNode.simple_name(name)
        doset = False
        if simple not in self.pretty_names:
            pretty = name
            doset = True
        else:
            pretty = self.pretty_names[simple]
        OrderedDict.__setitem__(self, pretty, value)
        if doset:
            self.pretty_names[simple] = pretty

    def __delitem__(self, name):
        simple = IBISNode.simple_name(name)
        pretty = self.pretty_names.pop(simple)
        OrderedDict.__delitem__(self, pretty)

    def __contains__(self, name):
        return IBISNode.simple_name(name) in self.pretty_names

class container(object):
    def __init__(self):
        self.data = None

class Node(container):
    """Intermediate parse results holder."""
    def __init__(self):
        super(Node, self).__init__()
        self.children = IBISNode()
        self.parser = None
        self.parent = None
        self.data = None

    def __str__(self):
        return str(self.data)
    __repr__ = __str__

    def add(self, child):
        orig = container()
        if child.parser.key in self.children:
            orig.data = self.children[child.parser.key]
        else:
            # FIXME: Double assign of initial?
            orig.data = copy.deepcopy(child.parser.initvalue)
        child.parser.merge(orig, child)
        self.children[child.parser.key] = orig.data

    def __iadd__(self, child):
        self.add(child)
        return self

class Parse(object):
    """Base pybis Parse object."""
    def __init__(self, key, pyparser=None, default=None, initvalue=None, data_name=None, list_merge=False, asList=False, asDict=False, required=False):
        """key:         Name of element.
           pyparser:    Parser to call with pyparse
           default:     Default value of object if not found
           initvalue:   Default value of object on first merge
           data_name:   Make the data of this node a child with name 'data_name'
           list_merge:  Merge multiple copies together as list
           asList:      Interpret pyparse results as a list
           asDict:      Interpret pyparse results as a dict
           required:    raise Exception if not found
        """
        self.key = key
        self.flat_key = key.replace(' ', '_').lower()
        self.data_name = data_name
        self.default = default
        self.initvalue = initvalue
        self.pyparser = pyparser
        self.list_merge = list_merge
        if list_merge and initvalue is None:
            self.initvalue = list()
        self.asList = asList
        self.asDict = asDict
        self.children = OrderedDict()
        self.parent = None
        self.globals = None
        self.required = required

    def add(self, obj):
        obj.parent = self
        self.children[obj.key] = obj

    def __iadd__(self, obj):
        self.add(obj)
        return self

    def get_globals(self):
        """Get the global parse object, for things that are parseable in all contexts."""
        if self.globals is None and self.parent is not None:
            self.globals = self.parent.get_globals()
        return self.globals

    def find_parser(self, text):
        """Find a child parser that can parse 'text'."""
        for name, child in self.children.items():
            if child.can_parse(text):
                return child
        if self.get_globals() is not None:
            return self.globals.find_parser(text)
        return None

    def can_parse(self, text):
        """True if we can parse 'text'."""
        return False

    def initial(self, text, comment):
        """Parse the first line of text and return a Node object."""
        node = Node()
        node.parser = self
        # Fill in defaults
        if self.initvalue is not None and self.default is None:
            # FIXME: Maybe we can shortcut merge here...
            node.data = copy.deepcopy(self.initvalue)
        for key, parser in self.children.items():
            if parser.default is not None:
                node.children[key] = copy.deepcopy(parser.default)
        return node

    def parse(self, node, text, comment):
        """Parse a subsequent line of text, False means we can't."""
        return not text

    def pyparse(self, text):
        """Use self.pyparser to parse 'text', returns parse tokens.
           Returns 'text' if there is no pyparser object.
        """
        if self.pyparser is not None:
            try:
                return self.pyparser.parse_string(text, parseAll=True)
            except ParseException as e:
                raise Exception("Failed to parse '{0}': {1}".format(text, e.msg))
        else:
            return text.strip()

    def fin(self, node):
        """Add a node to the parent."""
        if node.data is not None:
            self.flatten(node)
            if self.data_name:
                node.children[self.data_name] = node.data

        for name, p in self.children.items():
            if p.required and not name in node.children:
                raise Exception("'{0}' is missing required '{1}'".format(self.key, name))

        if node.children:
            node.data = node.children

        # Oi, so some vendors think it'd be funny to add sections, such as
        # [Series Pin Mapping] without any data
        if node.data is not None and node.parent:
             node.parent += node

    def pop(self, new, name):
        """Remove 'name' from ParseResults 'new'."""
        ret = new.data[name]
        del new.data[name]
        if len(list(new.data.keys())) == 0:
            for i, item in enumerate(new.data.asList()):
                if item == ret:
                    del new.data[i]
                    break
        return ret

    def flatten(self, new):
        """Reformat pyparse results as what we'd expect."""
        if isinstance(new.data, ParseResults):
            if self.asList:
                new.data = new.data.asList()
            elif self.asDict:
                new.data = IBISNode(new.data.asDict())
            elif not len(new.data):
                new.data = None
            else:
                new.data = new.data[0]

    def merge(self, orig, new):
        """Merge two instances of the same parser under one parent."""
        if self.list_merge:
            # FIXME: Maybe specify this behavior?
            if isinstance(new.data, list):
                orig.data.extend(new.data)
            else:
                orig.data.append(new.data)
        elif orig.data != self.initvalue and orig.data != self.default:
            raise Exception("'{0}' already assigned".format(self.key))
        else:
            orig.data = new.data

class Bracket(Parse):
    """An item that starts with '[<name>] ...'"""
    def can_parse(self, text):
        # FIXME: can_parse seems like a wasteful linear serach
        if text and text[0] == '[':
            sectionName, _, sectionText = text[1:].partition(']')
            sectionName = sectionName.replace(' ', '_').lower()
            if sectionName == self.flat_key:
                return True
        return False

    def initial(self, text, comment):
        node = super(Bracket, self).initial(text, comment)
        sectionName, _, sectionText = text[1:].partition(']')
        node.sectionText = sectionText.lstrip()
        return node

class Comment(Bracket):
    """IBIS '[Comment Char]' keyword."""
    def __init__(self, comment_holder):
        super(Comment, self).__init__("Comment Char")
        self.holder = comment_holder

    def initial(self, text, comment):
        node = super(Comment, self).initial(text, comment)

        if len(node.sectionText) == 0:
            # sectionText probably got commented out, no comment char change.
            pass
        elif not node.sectionText[1:].startswith("_char"):
            raise Exception("Invalid format, expected '<char>_char'")
        elif not node.sectionText[0] in "!\"#$%&'()*,:;<>?@\\^`{|}~":
            raise Exception("Invalid comment char, '{0}'".format(node.sectionText[0]))
        else:
            self.holder[0] = node.sectionText[0]
        return node

    def fin(self, node):
        """Ignore this node."""
        pass

class End(Bracket):
    """IBIS '[End]' keyword."""
    def initial(self, text, comment):
        """Return None as node, special meaning to close out parent."""
        return None

class Keyword(Bracket):
    """[<keyword>] <data>."""
    def initial(self, text, comment):
        node = super(Keyword, self).initial(text, comment)
        if not node.sectionText:
            raise Exception("Expected text after '{0}'".format(text))
        node.data = self.pyparse(node.sectionText)
        return node

class Text(Bracket):
    """IBIS text section, such as '[Copyright]'
       Since some IBIS files include important copyright data in comments
       below the keyword, we capture all text, including comments.
    """
    def __init__(self, key, comments=True, **kwds):
        self.comments = comments
        super(Text, self).__init__(key, **kwds)

    def initial(self, text, comment):
        node = super(Text, self).initial(text, comment)
        self.parse(node, node.sectionText, comment)
        return node

    def parse(self, node, text, comment):
        if self.comments:
            if len(text) and len(comment):
                text += ' '
            text += comment
        if node.data:
            if len(node.data) and len(text):
                node.data += '\n'
            node.data += text
        else:
            node.data = text
        return True

    def merge(self, orig, new):
        """Just merge together subsequent entries."""
        if orig.data is None:
            orig.data = ""
        if len(orig.data):
            orig.data += '\n'
        orig.data += new.data

    def flatten(self, node):
        node.data = node.data.strip('\n')

class Section(Bracket):
    """Multi-line token, such as '[Model]'"""
    def __init__(self, key, pyparser=None, labeled=False, **kwds):
        """If a Section is labeled, the data is an OrderedDict of objects
           indexed by sectionText.
        """
        self.needs_text = labeled
        if labeled:
            kwds["initvalue"] = OrderedDict()
            self.merge = self.labeled_merge
        super(Section, self).__init__(key, pyparser, **kwds)

    def initial(self, text, comment):
        node = super(Section, self).initial(text, comment)
        if self.needs_text and not node.sectionText:
            raise Exception("Expected text after '{0}'".format(text))
        elif not self.needs_text and node.sectionText:
            raise Exception("Unexpected text after keyword, '{0}'".format(node.sectionText))
        node.key = node.sectionText
        return node

    def labeled_merge(self, orig, new):
        if new.key in orig.data:
            raise Exception("'{0}' already contains '{1}'".format(self.key, new.key))
        orig.data[new.key] = new.data

class TokenizeSection(Section):
    """Text from entire section is collected and parsed with pyparser."""
    def __init__(self, key, pyparser=None, **kwds):
        super(TokenizeSection, self).__init__(key, pyparser, **kwds)

    def initial(self, text, comment):
        node = super(TokenizeSection, self).initial(text, comment)
        node.text = ""
        return node

    def parse(self, node, text, comment):
        node.text += text
        node.text += '\n'
        return True

    def fin(self, node):
        node.data = self.pyparse(node.text)
        super(TokenizeSection, self).fin(node)

class TableSection(Section):
    """[<section name>] <col1 name> <col2 name> <...>
       <row name> <col1 data> <col2 data> <...>
    """
    def __init__(self, key, pyparser=None, headers=[], optional=[], **kwds):
        kwds["initvalue"] = OrderedDict()
        super(TableSection, self).__init__(key, pyparser, **kwds)
        self.needs_text = True
        self.parsers = OrderedDict()
        self.headers = [ key.lower() for key in headers ]
        self.optional = [ key.lower() for key in optional ]

    def initial(self, text, comment):
        node = super(TableSection, self).initial(text, comment)
        node.keys = node.sectionText.lower().split()
        if len(node.keys) != len(set(node.keys)):
            raise Exception("'{0}' contains duplicates".format(node.sectionText))
        for key in self.headers:
            if key not in node.keys:
                raise Exception("Expected header names to contain '{0}'".format(key))
        for key in node.keys:
            if key not in self.headers and key not in self.optional:
                raise Exception("Unexpected header name, '{0}'".format(key))
        for key in self.optional:
            if key not in node.keys:
                node.keys.append(key)
        return node

    def assign_row(self, node, key, row):
        """For Seires_Pin_Mapping to override."""
        if key in node.data:
            raise Exception("'{0}' already contains '{1}'".format(self.key, key))
        node.data[key] = row

    def parse(self, node, text, comment):
        if super(TableSection, self).parse(node, text, comment):
            return True

        tokens = text.split()
        if not tokens:
            return True

        row = IBISNode(zip(node.keys, tokens[1:]))
        for name, parser in self.parsers.items():
            name = name.lower()
            if name in row:
                tmp = Node()
                try:
                    tmp.data = parser.parse_string(row[name], parseAll=True)
                except ParseException as e:
                    raise Exception("Failed to parse '{0}', '{1}': {2}".format(name, row[name], e.msg))
                self.flatten(tmp)
                row[name] = tmp.data
        for key in self.headers:
            if key not in row:
                raise Exception("Required column '{0}' missing".format(key))
        for key in self.optional:
            if key not in row:
                row[key] = None

        self.assign_row(node, tokens[0], row)
        return True

    def add(self, obj):
        if isinstance(obj, ParserElement):
            self.parsers[obj.resultsName] = obj
        else:
            super(Section, self).add(obj)
