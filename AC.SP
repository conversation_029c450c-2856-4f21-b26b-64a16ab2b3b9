* IBIS Buffer Test - Using SPICE Subcircuit
.option post
.tran 0.05n 70n

* input source
v1 in  0 pulse ( 0V 1V 1n 1n 1n 9n 20n )
r1 in  in1 50

* tristate enable 
v5 enable  0 1V 

* 电源和地
VDD1 nd_pu 0 DC 5V
VSS1 nd_pd 0 DC 0V
VDD2 nd_pc 0 DC 5V
VSS2 nd_gc 0 DC 0V

* transmission line
R_line n1 n2 50

* AT16245_OUT 输出缓冲器  
* 引脚顺序：DATA VCC GND OUT ENABLE
XTOP_MODEL_S_CLMP in1 nd_pu nd_pd n1 enable TOP_MODEL_S_CLMP

* load
rload n2 0 50

* 包含生成的SPICE子电路文件
.include  TOP.sp


.control
run
plot v(in) V(in1)

plot V(nd_pu) V(nd_pd) V(n1) V(in1) V(enable)
plot v(n2)
.endc
.end