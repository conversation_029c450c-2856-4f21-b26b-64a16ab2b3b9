#!/usr/bin/env python

from distutils.core import setup

setup(name = 'pybis',
    version = '0.10',
    description = 'Python IBIS parser',
    author = '<PERSON>ll',
    requires = [ 'pyparsing', 'numpy' ],
    author_email = '<PERSON>.<EMAIL>',
    url = 'https://github.com/russdill/pybis/wiki',
    download_url = 'https://github.com/russdill/pybis',
    py_modules = [ 'pybis' ],
    scripts = [ 'examples/models.py', 'examples/ibs2symdef.py' ],
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)'
        'Operating System :: OS Independent',
        'Programming Language :: Python',
        ]
    )
