#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Convert f-strings to Python 2 compatible format strings
"""

import re
import sys

def convert_fstring_to_format(content):
    """Convert f-strings to .format() calls"""
    
    # Pattern to match f-strings
    # This is a simplified pattern - real f-string parsing is complex
    fstring_pattern = r'f"([^"]*)"'
    
    def replace_fstring(match):
        fstring_content = match.group(1)
        
        # Find variables in curly braces
        var_pattern = r'\{([^}]+)\}'
        variables = re.findall(var_pattern, fstring_content)
        
        # Replace variables with positional placeholders
        format_string = fstring_content
        for i, var in enumerate(variables):
            format_string = format_string.replace('{' + var + '}', '{' + str(i) + '}')
        
        # Build the .format() call
        if variables:
            format_call = '"{0}".format({1})'.format(format_string, ', '.join(variables))
        else:
            format_call = '"{0}"'.format(format_string)
        
        return format_call
    
    # Replace f-strings with single quotes too
    fstring_pattern_single = r"f'([^']*)'"
    
    def replace_fstring_single(match):
        fstring_content = match.group(1)
        
        # Find variables in curly braces
        var_pattern = r'\{([^}]+)\}'
        variables = re.findall(var_pattern, fstring_content)
        
        # Replace variables with positional placeholders
        format_string = fstring_content
        for i, var in enumerate(variables):
            format_string = format_string.replace('{' + var + '}', '{' + str(i) + '}')
        
        # Build the .format() call
        if variables:
            format_call = '"{0}".format({1})'.format(format_string, ', '.join(variables))
        else:
            format_call = '"{0}"'.format(format_string)
        
        return format_call
    
    # Apply replacements
    content = re.sub(fstring_pattern, replace_fstring, content)
    content = re.sub(fstring_pattern_single, replace_fstring_single, content)
    
    return content

def convert_print_statements(content):
    """Convert print function calls to print statements for Python 2"""
    # This is a simple conversion - doesn't handle all cases
    # For now, we'll keep print as function since it's more compatible
    return content

def convert_string_methods(content):
    """Convert Python 3 specific string methods"""
    # Replace str.maketrans with a Python 2 compatible version
    content = content.replace('str.maketrans', 'string.maketrans')
    
    # Replace .startswith() with tuple argument (Python 2 compatible)
    # This is already compatible, no change needed
    
    return content

def main():
    if len(sys.argv) != 3:
        print("Usage: python convert_fstring.py input_file output_file")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(input_file, 'r') as f:
            content = f.read()
        
        # Apply conversions
        content = convert_fstring_to_format(content)
        content = convert_print_statements(content)
        content = convert_string_methods(content)
        
        # Fix the str.maketrans issue specifically
        content = content.replace('ibis_trans = str.maketrans', 'import string\n    ibis_trans = string.maketrans')
        
        with open(output_file, 'w') as f:
            f.write(content)
        
        print("Conversion completed: {0} -> {1}".format(input_file, output_file))
        
    except Exception as e:
        print("Error: {0}".format(e))
        sys.exit(1)

if __name__ == "__main__":
    main()
