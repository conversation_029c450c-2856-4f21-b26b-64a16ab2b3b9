* NGSPICE test circuit using converted SPICE subcircuit
* AT16245_OUT model simulation
* Comparison test for IBIS vs SPICE subcircuit

.title AT16245_OUT SPICE Subcircuit Test - NGSPICE

* Include SPICE subcircuit model
.include test_cmdline_input.sp

* Supply voltages
VDD VDD 0 DC 5.0
VSS VSS 0 DC 0

* Input stimulus for timing analysis
VIN_DATA DATA 0 PULSE(0 5 2n 0.5n 0.5n 8n 20n)
VIN_ENABLE ENABLE 0 PULSE(0 5 1n 0.1n 0.1n 18n 20n)

* Load network (typical PCB trace + receiver)
* Transmission line model (NGSPICE format)
T1 OUT 0 PAD_RCV 0 Z0=50 TD=1n

* Receiver load
CL_RCV PAD_RCV 0 5p
RL_RCV PAD_RCV 0 1MEG

* Additional parasitic elements
* Package parasitics
LPKG VDD VDD_PKG 2n
CPKG VDD_PKG 0 1p
RPKG VDD VDD_PKG 0.1

LPKG_GND VSS_PKG VSS 2n
CPKG_GND VSS_PKG 0 1p
RPKG_GND VSS_PKG VSS 0.1

* PCB trace parasitics
LTRACE OUT PAD_TRACE 5n
CTRACE PAD_TRACE 0 2p

* SPICE subcircuit instance
* Note: PAD is the main I/O pin, PAD_RISE and PAD_FALL are reference waveforms
XBUF PAD VDD_PKG VSS_PKG OUT ENABLE PAD_RISE PAD_FALL AT16245_OUT

* Input driver to control the buffer
* Voltage controlled switch to simulate input data
EDATA DATA_INT 0 DATA 0 1
RDATA DATA_INT OUT 1k

* Alternative: Direct connection for testing
* Connect DATA to internal buffer control (simplified model)
VDATA_CTRL DATA_CTRL 0 PWL(0 0 2n 0 3n 5 18n 5 19n 0)

* Load resistors for waveform reference outputs
RRISE PAD_RISE 0 1MEG
RFALL PAD_FALL 0 1MEG

* Analysis setup
.option reltol=1e-6 abstol=1e-12 vntol=1e-6
.option method=gear

* Transient analysis
.tran 0.01n 25n

* Control section for NGSPICE
.control
* Run the simulation
run

* Measurements using NGSPICE syntax
* Rise time (10% to 90%)
meas tran trise trig v(OUT) val=0.5 rise=1 targ v(OUT) val=4.5 rise=1

* Fall time (90% to 10%)
meas tran tfall trig v(OUT) val=4.5 fall=1 targ v(OUT) val=0.5 fall=1

* Propagation delays
meas tran tphl trig v(DATA) val=2.5 fall=1 targ v(OUT) val=2.5 fall=1
meas tran tplh trig v(DATA) val=2.5 rise=1 targ v(OUT) val=2.5 rise=1

* Output voltage levels
meas tran voh max v(OUT) from=10n to=15n
meas tran vol min v(OUT) from=15n to=20n

* Current measurements
meas tran ioh max i(VDD) from=10n to=15n
meas tran iol max i(VSS) from=15n to=20n

* Power consumption
meas tran pwr_avg avg v(VDD)*i(VDD) from=2n to=22n

* Signal integrity checks
* Overshoot measurement
meas tran overshoot max v(OUT) from=2n to=6n
meas tran undershoot min v(OUT) from=12n to=16n

* Settling time
meas tran tsettle trig v(OUT) val=4.5 rise=1 targ v(OUT) val=4.75 cross=last

* Print results
print trise tfall tphl tplh voh vol ioh iol pwr_avg overshoot undershoot tsettle

* Plot waveforms
plot v(DATA) title 'Signal Waveforms'
plot v(OUT) title 'Signal Waveforms'
plot v(PAD_RCV) title 'Signal Waveforms'
plot v(ENABLE) title 'Signal Waveforms'
plot v(PAD_RISE) title 'Reference Waveforms'
plot v(PAD_FALL) title 'Reference Waveforms'
plot i(VDD) title 'Supply Currents'
plot i(VSS) title 'Supply Currents'

* Save data to file
write test_spice_results.raw v(DATA) v(OUT) v(PAD_RCV) v(PAD_RISE) v(PAD_FALL) i(VDD) i(VSS)

* AC analysis for frequency response
ac dec 100 1MEG 1G
plot vdb(OUT) vp(OUT) title 'Frequency Response'
.endc

* Additional models for NGSPICE compatibility
.model NMOS NMOS(Level=1 VTO=0.7 KP=120u GAMMA=0.4 PHI=0.65)
.model PMOS PMOS(Level=1 VTO=-0.7 KP=40u GAMMA=0.4 PHI=0.65)

.end
