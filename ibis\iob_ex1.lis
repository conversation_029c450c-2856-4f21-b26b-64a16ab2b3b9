 ****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
  Copyright (c) 1986 - 2025 by Synopsys, Inc. All Rights Reserved.              
  This software and the associated documentation are proprietary
  to Synopsys, Inc. This software may only be used in accordance
  with the terms and conditions of a written license agreement with
  Synopsys, Inc. All other use, reproduction, or distribution of
  this software is strictly prohibited.
  Input File: iob_ex1.sp                                                        
  Command line options: E:\hspice2019\Hspice_P-2019.06-SP1-1\WIN64\hspice.com -i iob_ex1.sp -o E:\hspice2019\Hspice_P-2019.06-SP1-1\Demo\hspice\ibis\iob_ex1.lis
  Start time: Tue Jun 24 14:10:16 2025
  lic:  
  lic: FLEXlm: SDK_12.3 
  lic: USER:   chenqi               HOSTNAME: DESKTOP-K7C3CEV 
  lic: HOSTID: 000c29e08aa6         PID:      26900 
  lic: Using FLEXlm license file: 
  lic: 27000@DESKTOP-K7C3CEV 
  lic: Checkout 1 hspice 
  lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12 
  lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-K7C3CEV 
  lic:   
  **info** (iob_ex1.sp:6) DC voltage reset to initial transient source value in source 0:v1. new dc=0.0000D+00
  **warning** (b4:19) Inconsistent data between Ramp and PU/PD data : Falling Waveform, where File = at16245.ibs, Model = AT16245_OUT
  
  **warning** (b4:19) Inconsistent data between Ramp and PU/PD data : Rising Waveform, where File = at16245.ibs, Model = AT16245_OUT
  
  
      
 *****************************************************************
 ******  option summary
 ******
 runlvl  = 3         bypass  = 2.0000    

 **info** rebuilding matrix with pivot option

 **info** dc convergence successful at Newton-Raphson method 
1****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
 ******  
 * ibis buffer test

 ****** operating point information tnom=  25.000 temp=  25.000 ******
 ****** operating point status is voltage   simulation time is     0.     
    node    =voltage      node    =voltage      node    =voltage

 +0:enable  =   1.0000  0:in      =   0.      0:in1     =   0.     
 +0:n1      = 298.4703p 0:n2      = 296.8474p 0:nd_gc   =   0.     
 +0:nd_pc   =   5.0000  0:nd_pd   =   0.      0:nd_pu   =   5.0000 
 +0:out_of_i=   0.     


          ***** job concluded
 ******  
 * ibis buffer test

 ****** job statistics summary tnom=  25.000 temp=  25.000 ******

  ******  HSPICE Threads Information  ******

  Command Line Threads Count :     1
  Available CPU Count        :     4
  Actual Threads Count       :     1


  ******  Circuit Statistics  ******
  # nodes       =      16 # elements   =       7
  # resistors   =       2 # capacitors =       0 # inductors   =       0
  # mutual_inds =       0 # vccs       =       0 # vcvs        =       0
  # cccs        =       0 # ccvs       =       0 # volt_srcs   =       2
  # curr_srcs   =       0 # diodes     =       0 # bjts        =       0
  # jfets       =       0 # mosfets    =       0 # U elements  =       0
  # T elements  =       0 # W elements =       1 # B elements  =       2
  # S elements  =       0 # P elements =       0 # va device   =       0
  # vector_srcs =       0 # N elements =       0


  ******  Runtime Statistics (seconds)  ******

  analysis           time    # points   tot. iter  conv.iter
  op point           0.01           1           5
  transient          0.02        1401        1116         518 rev=        18
  readin             0.05
  errchk             0.06
  setup              0.00
  output             0.00


           peak memory used         69.09 megabytes
           total cpu time            0.14 seconds
           total elapsed time        2.57 seconds
           job started at     14:10:16 06/24/2025
           job ended   at     14:10:19 06/24/2025
           job total runtime         2.57 seconds


  lic: Release hspice token(s) 
 lic: total license checkout elapse time:        2.43(s)
