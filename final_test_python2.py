#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Final comprehensive test for Python 2.7.5 compatibility
Tests both pybis.py and ibis2spice.py with real IBIS file
"""

from __future__ import print_function
import sys
import os
import subprocess

def test_python_version():
    """Test Python version"""
    print("=" * 60)
    print("Python Version Check")
    print("=" * 60)
    print("Python version: {0}".format(sys.version))
    
    if sys.version_info[0] == 2 and sys.version_info[1] == 7:
        print("SUCCESS: Running on Python 2.7")
        return True
    else:
        print("WARNING: Not running on Python 2.7")
        return True

def test_module_imports():
    """Test module imports"""
    print("\n" + "=" * 60)
    print("Module Import Tests")
    print("=" * 60)
    
    success = True
    
    try:
        import pybis
        print("SUCCESS: pybis.py imported successfully")
    except Exception as e:
        print("FAIL: Failed to import pybis.py: {0}".format(e))
        success = False
    
    try:
        import ibis2spice
        print("SUCCESS: ibis2spice.py imported successfully")
    except Exception as e:
        print("FAIL: Failed to import ibis2spice.py: {0}".format(e))
        success = False
    
    return success

def test_ibis_parsing():
    """Test IBIS file parsing"""
    print("\n" + "=" * 60)
    print("IBIS File Parsing Test")
    print("=" * 60)
    
    try:
        from pybis import IBSParser
        
        # Parse the at16245.ibs file
        print("Parsing ibis/at16245.ibs...")
        parser = IBSParser()
        with open('ibis/at16245.ibs', 'r') as f:
            root = parser.parse(f)
        
        print("SUCCESS: IBIS file parsed successfully")
        
        # Check models
        if 'Model' in root:
            models = root['Model']
            print("Found {0} models: {1}".format(len(models), list(models.keys())))
            
            if 'AT16245_OUT' in models:
                print("SUCCESS: Target model AT16245_OUT found")
                model = models['AT16245_OUT']
                print("Model type: {0}".format(model.get('Model_type', 'Unknown')))
                return True, root
            else:
                print("FAIL: Target model AT16245_OUT not found")
                return False, None
        else:
            print("FAIL: No models found in IBIS file")
            return False, None
            
    except Exception as e:
        print("FAIL: Failed to parse IBIS file: {0}".format(e))
        return False, None

def test_spice_conversion(root):
    """Test SPICE conversion"""
    print("\n" + "=" * 60)
    print("SPICE Conversion Test")
    print("=" * 60)
    
    try:
        from ibis2spice import convert_ibis_to_spice
        
        model_name = "AT16245_OUT"
        output_file = "final_test_output.sp"
        type_num = 0  # typical values
        
        print("Converting model {0} to SPICE...".format(model_name))
        success = convert_ibis_to_spice(root, model_name, output_file, type_num)
        
        if success and os.path.exists(output_file):
            print("SUCCESS: SPICE conversion completed")
            print("Output file: {0}".format(output_file))
            
            # Check file size
            file_size = os.path.getsize(output_file)
            print("Output file size: {0} bytes".format(file_size))
            
            if file_size > 100:  # Reasonable size check
                print("SUCCESS: Output file has reasonable size")
                return True
            else:
                print("FAIL: Output file too small")
                return False
        else:
            print("FAIL: SPICE conversion failed")
            return False
            
    except Exception as e:
        print("FAIL: Failed to convert to SPICE: {0}".format(e))
        return False

def test_command_line():
    """Test command line interface"""
    print("\n" + "=" * 60)
    print("Command Line Interface Test")
    print("=" * 60)
    
    try:
        cmd = [
            sys.executable, 
            "ibis2spice.py", 
            "ibis/at16245.ibs", 
            "AT16245_OUT", 
            "cmdline_test_output.sp", 
            "typ"
        ]
        
        print("Running command: {0}".format(" ".join(cmd)))
        result = subprocess.call(cmd)
        
        if result == 0 and os.path.exists("cmdline_test_output.sp"):
            print("SUCCESS: Command line interface works")
            return True
        else:
            print("FAIL: Command line interface failed")
            return False
            
    except Exception as e:
        print("FAIL: Command line test failed: {0}".format(e))
        return False

def main():
    """Main test function"""
    print("Python 2.7.5 Compatibility Test for pybis and ibis2spice")
    print("Testing with real IBIS file: ibis/at16245.ibs")
    print("Target model: AT16245_OUT")
    
    all_passed = True
    
    # Test Python version
    if not test_python_version():
        all_passed = False
    
    # Test module imports
    if not test_module_imports():
        all_passed = False
        print("\nSTOPPING: Module import failed")
        return 1
    
    # Test IBIS parsing
    success, root = test_ibis_parsing()
    if not success:
        all_passed = False
        print("\nSTOPPING: IBIS parsing failed")
        return 1
    
    # Test SPICE conversion
    if not test_spice_conversion(root):
        all_passed = False
    
    # Test command line interface
    if not test_command_line():
        all_passed = False
    
    # Final result
    print("\n" + "=" * 60)
    if all_passed:
        print("SUCCESS: ALL TESTS PASSED!")
        print("pybis.py and ibis2spice.py are fully compatible with Python 2.7.5")
    else:
        print("FAIL: Some tests failed")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
